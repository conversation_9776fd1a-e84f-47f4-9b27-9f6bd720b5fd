services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: jav_postgres
    environment:
      POSTGRES_DB: javdb
      POSTGRES_USER: javuser
      POSTGRES_PASSWORD: javpass123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - ./shared-database/data:/var/lib/postgresql/data
      - ./shared-database/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    restart: unless-stopped
    networks:
      - jav-network

  # Redis 缓存
  redis:
    image: redis:alpine
    container_name: jav_redis
    ports:
      - "6380:6379"
    restart: unless-stopped
    networks:
      - jav-network

  # JavBus API 服务
  javbus-api:
    build: ./javbus-api
    container_name: jav_javbus_api
    environment:
      - PORT=3000
      - DATABASE_URL=*********************************************/javdb
      - REDIS_URL=redis://redis:6379
    ports:
      - "3001:3000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - jav-network
    volumes:
      - ./storage:/app/storage

  # Javinizer 服务
  javinizer:
    build: ./javinizer
    container_name: jav_javinizer
    environment:
      - DATABASE_URL=*********************************************/javdb
      - STORAGE_PATH=/app/storage
    ports:
      - "8600:8600"
    volumes:
      - ./storage:/app/storage
      - ./javinizer/config:/app/config
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - jav-network

  # JavSP 服务
  javsp:
    build: ./javsp
    container_name: jav_javsp
    environment:
      - DATABASE_URL=*********************************************/javdb
      - STORAGE_PATH=/app/storage
    volumes:
      - ./storage:/app/storage
      - ./javsp/config.yml:/app/config.yml
    restart: unless-stopped
    networks:
      - jav-network

  # API 网关
  api-gateway:
    build: ./api-gateway
    container_name: jav_api_gateway
    environment:
      - PORT=8080
      - DATABASE_URL=*********************************************/javdb
      - REDIS_URL=redis://redis:6379
      - JAVBUS_API_URL=http://javbus-api:3000
      - JAVSP_API_URL=http://javsp:8000
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
      - javbus-api
      - javsp
    restart: unless-stopped
    networks:
      - jav-network

  # 数据保存服务
  data-service:
    build: ./data-service
    container_name: jav_data_service
    environment:
      - DATABASE_URL=*********************************************/javdb
      - STORAGE_PATH=/app/storage
      - API_GATEWAY_URL=http://api-gateway:8080
    volumes:
      - ./storage:/app/storage
    depends_on:
      - api-gateway
    restart: unless-stopped
    networks:
      - jav-network

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: jav_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./storage:/usr/share/nginx/html/storage
    depends_on:
      - api-gateway
    restart: unless-stopped
    networks:
      - jav-network

networks:
  jav-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
