{"name": "jav-data-service", "version": "1.0.0", "description": "JAV数据保存服务 - 专门处理图片下载和数据库存储", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "test": "jest", "lint": "eslint src/**/*.ts"}, "keywords": ["jav", "data", "storage", "images", "download"], "author": "JAV API Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bull": "^4.12.2", "dotenv": "^16.3.1", "express": "^4.18.2", "file-type": "^18.7.0", "fs-extra": "^11.2.0", "jimp": "^0.22.10", "mime-types": "^2.1.35", "node-cron": "^3.0.3", "pg": "^8.11.3", "redis": "^4.6.10", "sharp": "^0.32.6", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/mime-types": "^2.1.4", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/pg": "^8.15.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "jest": "^29.7.0", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}