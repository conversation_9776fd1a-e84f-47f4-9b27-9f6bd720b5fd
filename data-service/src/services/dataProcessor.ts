import { db } from '../server';
import { ImageDownloader } from './imageDownloader';
import { logger } from '../utils/logger';

export class DataProcessor {
  static async processMovieData(movieData: any, source: string = 'unknown'): Promise<any> {
    try {
      logger.info(`Processing movie data: ${movieData.id || movieData.num}`);
      
      // 1. 保存影片基础信息
      const movieId = await this.saveMovieInfo(movieData);
      if (!movieId) {
        throw new Error('Failed to save movie info');
      }
      
      // 2. 处理封面图片
      if (movieData.img || movieData.cover) {
        try {
          const coverUrl = movieData.img || movieData.cover;
          const coverFilename = `${movieData.id || movieData.num}_cover`;
          
          const coverResult = await ImageDownloader.downloadImage(
            coverUrl,
            'covers',
            coverFilename,
            { movie_id: movieData.id || movieData.num, type: 'cover' }
          );
          
          if (coverResult.success) {
            await this.updateMovieCover(movieId, coverResult.local_path);
          }
        } catch (error) {
          logger.warn(`Failed to download cover for ${movieData.id}:`, (error as Error).message);
        }
      }
      
      // 3. 处理演员信息
      if (movieData.stars || movieData.actress) {
        const actors = movieData.stars || 
                      (Array.isArray(movieData.actress) ? movieData.actress : [movieData.actress]);
        
        for (const actor of actors) {
          try {
            const actorId = await this.saveActorInfo(actor);
            if (actorId) {
              await this.linkMovieActor(movieId, actorId);
              
              // 下载演员头像
              if (actor.avatar || actor.thumb) {
                try {
                  const avatarUrl = actor.avatar || actor.thumb;
                  const avatarFilename = `${actor.id || actor.name.replace(/\s+/g, '_')}_avatar`;
                  
                  const avatarResult = await ImageDownloader.downloadImage(
                    avatarUrl,
                    'actors',
                    avatarFilename,
                    { actor_id: actor.id, actor_name: actor.name, type: 'avatar' }
                  );
                  
                  if (avatarResult.success) {
                    await this.updateActorAvatar(actorId, avatarResult.local_path);
                  }
                } catch (error) {
                  logger.warn(`Failed to download avatar for ${actor.name}:`, (error as Error).message);
                }
              }
            }
          } catch (error) {
            logger.warn(`Failed to process actor ${actor.name}:`, (error as Error).message);
          }
        }
      }
      
      // 4. 处理分类信息
      if (movieData.genres || movieData.genre) {
        const genres = movieData.genres || 
                      (Array.isArray(movieData.genre) ? movieData.genre : [movieData.genre]);
        
        for (const genre of genres) {
          try {
            const genreId = await this.saveGenreInfo(genre);
            if (genreId) {
              await this.linkMovieGenre(movieId, genreId);
            }
          } catch (error) {
            logger.warn(`Failed to process genre ${genre.name || genre}:`, (error as Error).message);
          }
        }
      }
      
      // 5. 处理磁力链接
      if (movieData.magnets && Array.isArray(movieData.magnets)) {
        for (const magnet of movieData.magnets) {
          try {
            await this.saveMagnetInfo(movieId, magnet);
          } catch (error) {
            logger.warn(`Failed to save magnet:`, (error as Error).message);
          }
        }
      }
      
      // 6. 处理预览图片（排除样品图）
      if (movieData.samples && Array.isArray(movieData.samples)) {
        const filteredSamples = movieData.samples.filter((sample: any) =>
          !this.isSampleImage(sample.src || sample.url, sample.alt)
        );
        
        for (let i = 0; i < filteredSamples.length; i++) {
          const sample = filteredSamples[i];
          try {
            const sampleFilename = `${movieData.id || movieData.num}_sample_${i + 1}`;
            
            const sampleResult = await ImageDownloader.downloadImage(
              sample.src || sample.url,
              'samples',
              sampleFilename,
              { 
                movie_id: movieData.id || movieData.num, 
                type: 'sample',
                index: i + 1,
                alt: sample.alt
              }
            );
            
            if (sampleResult.success) {
              await this.saveSampleImage(movieId, sample, sampleResult.local_path, i);
            }
          } catch (error) {
            logger.warn(`Failed to download sample image ${i + 1}:`, (error as Error).message);
          }
        }
      }
      
      // 7. 记录数据来源
      await this.recordMetadataSource(movieId, source, movieData.url, this.calculateDataQuality(movieData));
      
      logger.info(`Successfully processed movie: ${movieData.id || movieData.num}`);
      
      return {
        success: true,
        movie_id: movieData.id || movieData.num,
        database_id: movieId,
        source: source
      };
      
    } catch (error) {
      logger.error(`Failed to process movie data:`, error);
      throw error;
    }
  }
  
  private static async saveMovieInfo(movieData: any): Promise<string | null> {
    try {
      const query = `
        INSERT INTO movies (movie_id, title, original_title, release_date, duration, rating, plot, cover_url, trailer_url, gid, uc)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT (movie_id) 
        DO UPDATE SET 
          title = EXCLUDED.title,
          original_title = EXCLUDED.original_title,
          release_date = EXCLUDED.release_date,
          duration = EXCLUDED.duration,
          rating = EXCLUDED.rating,
          plot = EXCLUDED.plot,
          cover_url = EXCLUDED.cover_url,
          trailer_url = EXCLUDED.trailer_url,
          gid = EXCLUDED.gid,
          uc = EXCLUDED.uc,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `;
      
      const values = [
        movieData.id || movieData.num,
        movieData.title,
        movieData.original_title || movieData.originaltitle,
        movieData.date || movieData.release_date,
        movieData.videoLength || movieData.duration,
        movieData.rating,
        movieData.plot,
        movieData.img || movieData.cover,
        movieData.trailer,
        movieData.gid,
        movieData.uc
      ];
      
      const result = await db.query(query, values);
      return result.rows[0]?.id || null;
    } catch (error) {
      logger.error('Failed to save movie info:', error);
      return null;
    }
  }
  
  private static async saveActorInfo(actorData: any): Promise<string | null> {
    try {
      const actorId = actorData.id || actorData.name?.replace(/\s+/g, '_').toLowerCase();
      
      const query = `
        INSERT INTO actors (actor_id, name, japanese_name, english_name, birthday, height, bust, waist, hip, birthplace, hobby, avatar_url)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        ON CONFLICT (actor_id) 
        DO UPDATE SET 
          name = EXCLUDED.name,
          japanese_name = EXCLUDED.japanese_name,
          english_name = EXCLUDED.english_name,
          birthday = EXCLUDED.birthday,
          height = EXCLUDED.height,
          bust = EXCLUDED.bust,
          waist = EXCLUDED.waist,
          hip = EXCLUDED.hip,
          birthplace = EXCLUDED.birthplace,
          hobby = EXCLUDED.hobby,
          avatar_url = EXCLUDED.avatar_url,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `;
      
      const values = [
        actorId,
        actorData.name,
        actorData.japanese_name || actorData.altname,
        actorData.english_name,
        actorData.birthday,
        actorData.height ? parseInt(actorData.height) : null,
        actorData.bust ? parseInt(actorData.bust) : null,
        actorData.waist || actorData.waistline ? parseInt(actorData.waist || actorData.waistline) : null,
        actorData.hip || actorData.hipline ? parseInt(actorData.hip || actorData.hipline) : null,
        actorData.birthplace,
        actorData.hobby,
        actorData.avatar || actorData.thumb
      ];
      
      const result = await db.query(query, values);
      return result.rows[0]?.id || null;
    } catch (error) {
      logger.error('Failed to save actor info:', error);
      return null;
    }
  }
  
  private static async saveGenreInfo(genreData: any): Promise<string | null> {
    try {
      const genreId = genreData.id || genreData.name?.toLowerCase().replace(/\s+/g, '_');
      const genreName = genreData.name || genreData;
      
      const query = `
        INSERT INTO genres (genre_id, name, japanese_name)
        VALUES ($1, $2, $3)
        ON CONFLICT (genre_id) 
        DO UPDATE SET 
          name = EXCLUDED.name,
          japanese_name = EXCLUDED.japanese_name
        RETURNING id
      `;
      
      const result = await db.query(query, [genreId, genreName, genreData.japanese_name]);
      return result.rows[0]?.id || null;
    } catch (error) {
      logger.error('Failed to save genre info:', error);
      return null;
    }
  }
  
  private static async linkMovieActor(movieId: string, actorId: string): Promise<void> {
    try {
      const query = `
        INSERT INTO movie_actors (movie_id, actor_id, role)
        VALUES ($1, $2, 'actress')
        ON CONFLICT (movie_id, actor_id) DO NOTHING
      `;
      await db.query(query, [movieId, actorId]);
    } catch (error) {
      logger.error('Failed to link movie and actor:', error);
    }
  }
  
  private static async linkMovieGenre(movieId: string, genreId: string): Promise<void> {
    try {
      const query = `
        INSERT INTO movie_genres (movie_id, genre_id)
        VALUES ($1, $2)
        ON CONFLICT (movie_id, genre_id) DO NOTHING
      `;
      await db.query(query, [movieId, genreId]);
    } catch (error) {
      logger.error('Failed to link movie and genre:', error);
    }
  }
  
  private static async saveMagnetInfo(movieId: string, magnetData: any): Promise<void> {
    try {
      const query = `
        INSERT INTO magnets (movie_id, magnet_id, link, title, size_text, size_bytes, share_date, is_hd, has_subtitle)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (magnet_id) DO NOTHING
      `;
      
      const values = [
        movieId,
        magnetData.id,
        magnetData.link,
        magnetData.title,
        magnetData.size,
        magnetData.numberSize || magnetData.size_bytes,
        magnetData.shareDate || magnetData.share_date,
        magnetData.isHD || magnetData.is_hd || false,
        magnetData.hasSubtitle || magnetData.has_subtitle || false
      ];
      
      await db.query(query, values);
    } catch (error) {
      logger.error('Failed to save magnet info:', error);
    }
  }
  
  private static async saveSampleImage(movieId: string, sampleData: any, localPath: string, sortOrder: number): Promise<void> {
    try {
      const query = `
        INSERT INTO sample_images (movie_id, image_id, url, thumbnail_url, local_path, alt_text, sort_order)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (movie_id, image_id) DO NOTHING
      `;
      
      const values = [
        movieId,
        sampleData.id || `sample_${sortOrder}`,
        sampleData.src || sampleData.url,
        sampleData.thumbnail,
        localPath,
        sampleData.alt,
        sortOrder
      ];
      
      await db.query(query, values);
    } catch (error) {
      logger.error('Failed to save sample image:', error);
    }
  }
  
  private static async updateMovieCover(movieId: string, localPath: string): Promise<void> {
    try {
      const query = `UPDATE movies SET cover_local_path = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2`;
      await db.query(query, [localPath, movieId]);
    } catch (error) {
      logger.error('Failed to update movie cover:', error);
    }
  }
  
  private static async updateActorAvatar(actorId: string, localPath: string): Promise<void> {
    try {
      const query = `UPDATE actors SET avatar_local_path = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2`;
      await db.query(query, [localPath, actorId]);
    } catch (error) {
      logger.error('Failed to update actor avatar:', error);
    }
  }
  
  private static async recordMetadataSource(movieId: string, source: string, sourceUrl?: string, dataQuality: number = 50): Promise<void> {
    try {
      const query = `
        INSERT INTO metadata_sources (movie_id, source_name, source_url, data_quality)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (movie_id, source_name) 
        DO UPDATE SET 
          source_url = EXCLUDED.source_url,
          data_quality = EXCLUDED.data_quality,
          scraped_at = CURRENT_TIMESTAMP
      `;
      await db.query(query, [movieId, source, sourceUrl, dataQuality]);
    } catch (error) {
      logger.error('Failed to record metadata source:', error);
    }
  }
  
  private static isSampleImage(url: string, alt?: string): boolean {
    const sampleKeywords = ['sample', 'watermark', 'preview', 'demo'];
    const urlLower = url.toLowerCase();
    const altLower = alt?.toLowerCase() || '';
    
    return sampleKeywords.some(keyword => 
      urlLower.includes(keyword) || altLower.includes(keyword)
    );
  }
  
  private static calculateDataQuality(movieData: any): number {
    let score = 0;
    
    if (movieData.title) score += 20;
    if (movieData.id || movieData.num) score += 15;
    if (movieData.img || movieData.cover) score += 15;
    if (movieData.plot) score += 10;
    if (movieData.date || movieData.release_date) score += 10;
    if (movieData.stars || movieData.actress) score += 15;
    if (movieData.genres || movieData.genre) score += 10;
    if (movieData.magnets && movieData.magnets.length > 0) score += 5;
    
    return Math.min(score, 100);
  }
}
