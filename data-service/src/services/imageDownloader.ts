import axios from 'axios';
import sharp from 'sharp';
import fs from 'fs-extra';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
// import { fileTypeFromBuffer } from 'file-type';
import { logger } from '../utils/logger';

export class ImageDownloader {
  private static readonly STORAGE_BASE = process.env.STORAGE_PATH || '/app/storage';
  private static readonly TEMP_DIR = path.join(this.STORAGE_BASE, 'temp');
  private static readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private static readonly TIMEOUT = 30000; // 30秒
  
  static async downloadImage(
    url: string,
    category: 'covers' | 'actors' | 'samples',
    filename: string,
    metadata: any = {}
  ): Promise<any> {
    try {
      // 确保目录存在
      const categoryDir = path.join(this.STORAGE_BASE, category);
      await fs.ensureDir(categoryDir);
      await fs.ensureDir(this.TEMP_DIR);
      
      logger.info(`Starting download: ${url}`);
      
      // 下载图片
      const response = await axios({
        method: 'GET',
        url: url,
        responseType: 'arraybuffer',
        timeout: this.TIMEOUT,
        maxContentLength: this.MAX_FILE_SIZE,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache'
        }
      });
      
      const buffer = Buffer.from(response.data);
      
      // 简单验证：检查响应头的content-type
      const contentType = response.headers['content-type'];
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error('Downloaded file is not a valid image');
      }
      
      // 验证文件大小
      if (buffer.length < 1024) {
        throw new Error('Downloaded file too small, likely an error page');
      }
      
      // 生成文件名，从URL或content-type推断扩展名
      let ext = 'jpg'; // 默认扩展名
      if (contentType.includes('png')) ext = 'png';
      else if (contentType.includes('gif')) ext = 'gif';
      else if (contentType.includes('webp')) ext = 'webp';

      const finalFilename = `${filename}.${ext}`;
      const tempPath = path.join(this.TEMP_DIR, `${uuidv4()}.${ext}`);
      const finalPath = path.join(categoryDir, finalFilename);
      
      // 保存原始文件到临时目录
      await fs.writeFile(tempPath, buffer);
      
      // 处理图片
      const processedPath = await this.processImage(tempPath, finalPath, category, metadata);
      
      // 清理临时文件
      await fs.remove(tempPath);
      
      const stats = await fs.stat(processedPath);
      const relativePath = path.relative(this.STORAGE_BASE, processedPath);
      
      logger.info(`Image downloaded successfully: ${relativePath} (${this.formatBytes(stats.size)})`);
      
      return {
        success: true,
        url: url,
        local_path: relativePath,
        filename: finalFilename,
        size: stats.size,
        format: ext,
        category: category,
        metadata: metadata
      };
      
    } catch (error) {
      logger.error(`Failed to download image ${url}:`, error);
      throw error;
    }
  }
  
  private static async processImage(
    inputPath: string,
    outputPath: string,
    category: 'covers' | 'actors' | 'samples',
    metadata: any
  ): Promise<string> {
    try {
      let pipeline = sharp(inputPath);
      
      // 获取图片信息
      const imageInfo = await pipeline.metadata();
      
      // 根据类别进行不同的处理
      switch (category) {
        case 'covers':
          pipeline = await this.processCoverImage(pipeline, imageInfo, metadata);
          break;
        case 'actors':
          pipeline = await this.processActorImage(pipeline, imageInfo, metadata);
          break;
        case 'samples':
          pipeline = await this.processSampleImage(pipeline, imageInfo, metadata);
          break;
      }
      
      // 保存处理后的图片
      await pipeline.jpeg({ quality: 90, progressive: true }).toFile(outputPath);
      
      return outputPath;
      
    } catch (error) {
      logger.error('Image processing failed:', error);
      // 如果处理失败，直接复制原文件
      await fs.copy(inputPath, outputPath);
      return outputPath;
    }
  }
  
  private static async processCoverImage(
    pipeline: sharp.Sharp,
    imageInfo: sharp.Metadata,
    _metadata: any
  ): Promise<sharp.Sharp> {
    // 封面图片处理
    const targetWidth = 800;
    const targetHeight = 538;
    
    // 如果图片太大，进行缩放
    if (imageInfo.width && imageInfo.width > targetWidth) {
      pipeline = pipeline.resize(targetWidth, targetHeight, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }
    
    // 增强图片质量
    pipeline = pipeline.sharpen();
    
    return pipeline;
  }
  
  private static async processActorImage(
    pipeline: sharp.Sharp,
    imageInfo: sharp.Metadata,
    _metadata: any
  ): Promise<sharp.Sharp> {
    // 演员头像处理
    const targetSize = 400;
    
    // 如果图片太大，进行缩放
    if (imageInfo.width && imageInfo.width > targetSize) {
      pipeline = pipeline.resize(targetSize, targetSize, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }
    
    return pipeline;
  }
  
  private static async processSampleImage(
    pipeline: sharp.Sharp,
    imageInfo: sharp.Metadata,
    _metadata: any
  ): Promise<sharp.Sharp> {
    // 预览图片处理
    const maxWidth = 1200;
    
    // 如果图片太大，进行缩放
    if (imageInfo.width && imageInfo.width > maxWidth) {
      pipeline = pipeline.resize(maxWidth, null, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }
    
    return pipeline;
  }
  
  static async batchDownload(
    downloads: Array<{
      url: string;
      category: 'covers' | 'actors' | 'samples';
      filename: string;
      metadata?: any;
    }>
  ): Promise<any[]> {
    const results: any[] = [];
    const concurrency = 5; // 并发数限制
    
    for (let i = 0; i < downloads.length; i += concurrency) {
      const batch = downloads.slice(i, i + concurrency);
      
      const batchResults = await Promise.allSettled(
        batch.map(download => 
          this.downloadImage(download.url, download.category, download.filename, download.metadata)
        )
      );
      
      batchResults.forEach((result, index) => {
        const download = batch[index];
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            url: download.url,
            error: result.reason.message
          });
        }
      });
      
      // 批次间延迟，避免过于频繁的请求
      if (i + concurrency < downloads.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }
  
  static async validateImage(filePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(filePath);
      if (stats.size < 1024) return false;
      
      // 简单的文件扩展名检查
      const ext = path.extname(filePath).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'].includes(ext);
    } catch {
      return false;
    }
  }
  
  private static formatBytes(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }
}
