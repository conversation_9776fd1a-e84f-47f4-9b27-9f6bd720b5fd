import axios from 'axios';
import { logger } from '../utils/logger';

export interface AvatarQualityResult {
  url: string;
  quality_score: number;
  resolution?: string;
  file_size?: number;
  format?: string;
  issues: string[];
  source: string;
  width?: number;
  height?: number;
}

export interface QualityEvaluationOptions {
  checkResolution?: boolean;
  checkFileSize?: boolean;
  checkFormat?: boolean;
  checkAccessibility?: boolean;
  timeout?: number;
}

export class AvatarQualityEvaluator {
  private readonly PREFERRED_FORMATS = ['jpg', 'jpeg', 'png', 'webp'];
  private readonly MIN_RESOLUTION = { width: 200, height: 200 };
  private readonly PREFERRED_RESOLUTION = { width: 400, height: 400 };
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly MIN_FILE_SIZE = 5 * 1024; // 5KB

  /**
   * 评估头像质量
   */
  async evaluateAvatar(
    url: string,
    source: string,
    options: QualityEvaluationOptions = {}
  ): Promise<AvatarQualityResult> {
    const {
      checkResolution = true,
      checkFileSize = true,
      checkFormat = true,
      checkAccessibility = true,
      timeout = 10000
    } = options;

    const result: AvatarQualityResult = {
      url,
      source,
      quality_score: 0,
      issues: []
    };

    try {
      // 1. 检查URL可访问性
      if (checkAccessibility) {
        const accessibilityScore = await this.checkAccessibility(url, timeout);
        result.quality_score += accessibilityScore;
        
        if (accessibilityScore === 0) {
          result.issues.push('URL不可访问');
          return result;
        }
      }

      // 2. 获取图片信息
      const imageInfo = await this.getImageInfo(url, timeout);
      if (imageInfo) {
        result.width = imageInfo.width;
        result.height = imageInfo.height;
        result.file_size = imageInfo.file_size;
        result.format = imageInfo.format;
        result.resolution = `${imageInfo.width}x${imageInfo.height}`;
      }

      // 3. 检查图片格式
      if (checkFormat && imageInfo) {
        const formatScore = this.evaluateFormat(imageInfo.format);
        result.quality_score += formatScore;
        
        if (formatScore < 20) {
          result.issues.push(`不支持的图片格式: ${imageInfo.format}`);
        }
      }

      // 4. 检查分辨率
      if (checkResolution && imageInfo) {
        const resolutionScore = this.evaluateResolution(imageInfo.width, imageInfo.height);
        result.quality_score += resolutionScore;
        
        if (imageInfo.width < this.MIN_RESOLUTION.width || imageInfo.height < this.MIN_RESOLUTION.height) {
          result.issues.push(`分辨率过低: ${imageInfo.width}x${imageInfo.height}`);
        }
      }

      // 5. 检查文件大小
      if (checkFileSize && imageInfo && imageInfo.file_size) {
        const sizeScore = this.evaluateFileSize(imageInfo.file_size);
        result.quality_score += sizeScore;
        
        if (imageInfo.file_size > this.MAX_FILE_SIZE) {
          result.issues.push(`文件过大: ${this.formatFileSize(imageInfo.file_size)}`);
        } else if (imageInfo.file_size < this.MIN_FILE_SIZE) {
          result.issues.push(`文件过小: ${this.formatFileSize(imageInfo.file_size)}`);
        }
      }

      // 6. 检查图片比例
      if (imageInfo) {
        const aspectRatioScore = this.evaluateAspectRatio(imageInfo.width, imageInfo.height);
        result.quality_score += aspectRatioScore;
        
        const ratio = imageInfo.width / imageInfo.height;
        if (ratio < 0.7 || ratio > 1.5) {
          result.issues.push(`图片比例异常: ${ratio.toFixed(2)}`);
        }
      }

      // 确保分数在0-100范围内
      result.quality_score = Math.min(Math.max(result.quality_score, 0), 100);

    } catch (error) {
      logger.error('Avatar quality evaluation failed:', error);
      result.issues.push(`评估失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * 检查URL可访问性
   */
  private async checkAccessibility(url: string, timeout: number): Promise<number> {
    try {
      const response = await axios.head(url, {
        timeout,
        validateStatus: (status) => status < 500
      });

      if (response.status >= 200 && response.status < 300) {
        return 25; // 可访问性占25分
      } else if (response.status >= 300 && response.status < 400) {
        return 15; // 重定向，扣分
      } else {
        return 0; // 客户端错误
      }
    } catch (error) {
      return 0; // 网络错误或超时
    }
  }

  /**
   * 获取图片信息
   */
  private async getImageInfo(url: string, timeout: number): Promise<{
    width: number;
    height: number;
    format: string;
    file_size?: number;
  } | null> {
    try {
      const response = await axios.get(url, {
        timeout,
        responseType: 'arraybuffer',
        headers: {
          'Range': 'bytes=0-2048' // 只下载前2KB来获取图片信息
        },
        validateStatus: (status) => status < 500
      });

      const buffer = Buffer.from(response.data);
      const imageInfo = this.parseImageHeader(buffer);
      
      if (imageInfo) {
        // 尝试从响应头获取文件大小
        const contentLength = response.headers['content-length'];
        if (contentLength) {
          imageInfo.file_size = parseInt(contentLength);
        }
      }

      return imageInfo;
    } catch (error) {
      logger.debug('Failed to get image info:', error);
      return null;
    }
  }

  /**
   * 解析图片头部信息
   */
  private parseImageHeader(buffer: Buffer): {
    width: number;
    height: number;
    format: string;
  } | null {
    try {
      // JPEG
      if (buffer[0] === 0xFF && buffer[1] === 0xD8) {
        const jpegInfo = this.parseJPEGHeader(buffer);
        return jpegInfo ? { ...jpegInfo, format: 'jpeg' } : null;
      }

      // PNG
      if (buffer.slice(0, 8).equals(Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]))) {
        const pngInfo = this.parsePNGHeader(buffer);
        return pngInfo ? { ...pngInfo, format: 'png' } : null;
      }

      // WebP
      if (buffer.slice(0, 4).toString() === 'RIFF' && buffer.slice(8, 12).toString() === 'WEBP') {
        const webpInfo = this.parseWebPHeader(buffer);
        return webpInfo ? { ...webpInfo, format: 'webp' } : null;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 解析JPEG头部
   */
  private parseJPEGHeader(buffer: Buffer): { width: number; height: number } | null {
    let offset = 2;
    while (offset < buffer.length - 4) {
      if (buffer[offset] === 0xFF) {
        const marker = buffer[offset + 1];
        if (marker >= 0xC0 && marker <= 0xC3) {
          const height = buffer.readUInt16BE(offset + 5);
          const width = buffer.readUInt16BE(offset + 7);
          return { width, height };
        }
        offset += 2 + buffer.readUInt16BE(offset + 2);
      } else {
        offset++;
      }
    }
    return null;
  }

  /**
   * 解析PNG头部
   */
  private parsePNGHeader(buffer: Buffer): { width: number; height: number } | null {
    if (buffer.length >= 24) {
      const width = buffer.readUInt32BE(16);
      const height = buffer.readUInt32BE(20);
      return { width, height };
    }
    return null;
  }

  /**
   * 解析WebP头部
   */
  private parseWebPHeader(buffer: Buffer): { width: number; height: number } | null {
    if (buffer.length >= 30 && buffer.slice(12, 16).toString() === 'VP8 ') {
      const width = buffer.readUInt16LE(26) & 0x3FFF;
      const height = buffer.readUInt16LE(28) & 0x3FFF;
      return { width, height };
    }
    return null;
  }

  /**
   * 评估图片格式
   */
  private evaluateFormat(format: string): number {
    const normalizedFormat = format.toLowerCase();
    
    if (normalizedFormat === 'jpeg' || normalizedFormat === 'jpg') return 25;
    if (normalizedFormat === 'png') return 25;
    if (normalizedFormat === 'webp') return 25;
    if (normalizedFormat === 'gif') return 15;
    
    return 0;
  }

  /**
   * 评估分辨率
   */
  private evaluateResolution(width: number, height: number): number {
    const minArea = this.MIN_RESOLUTION.width * this.MIN_RESOLUTION.height;
    const preferredArea = this.PREFERRED_RESOLUTION.width * this.PREFERRED_RESOLUTION.height;
    const currentArea = width * height;

    if (currentArea < minArea) return 0;
    if (currentArea >= preferredArea) return 25;
    
    // 线性插值
    const ratio = (currentArea - minArea) / (preferredArea - minArea);
    return Math.round(ratio * 25);
  }

  /**
   * 评估文件大小
   */
  private evaluateFileSize(fileSize: number): number {
    if (fileSize < this.MIN_FILE_SIZE) return 0;
    if (fileSize > this.MAX_FILE_SIZE) return 5;
    
    // 理想大小范围：50KB - 500KB
    const idealMin = 50 * 1024;
    const idealMax = 500 * 1024;
    
    if (fileSize >= idealMin && fileSize <= idealMax) return 15;
    if (fileSize < idealMin) return Math.round((fileSize / idealMin) * 15);
    
    // 超过理想大小但未超过最大限制
    const penalty = Math.min((fileSize - idealMax) / (this.MAX_FILE_SIZE - idealMax), 1);
    return Math.round(15 * (1 - penalty * 0.5));
  }

  /**
   * 评估图片比例
   */
  private evaluateAspectRatio(width: number, height: number): number {
    const ratio = width / height;
    
    // 理想比例：0.8 - 1.2 (接近正方形)
    if (ratio >= 0.8 && ratio <= 1.2) return 10;
    if (ratio >= 0.7 && ratio <= 1.5) return 7;
    if (ratio >= 0.6 && ratio <= 1.8) return 3;
    
    return 0;
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  }

  /**
   * 批量评估头像质量
   */
  async batchEvaluateAvatars(
    avatars: Array<{ url: string; source: string }>,
    options: QualityEvaluationOptions = {}
  ): Promise<AvatarQualityResult[]> {
    const results: AvatarQualityResult[] = [];

    for (const avatar of avatars) {
      try {
        const result = await this.evaluateAvatar(avatar.url, avatar.source, options);
        results.push(result);
      } catch (error) {
        logger.error(`Failed to evaluate avatar ${avatar.url}:`, error);
        results.push({
          url: avatar.url,
          source: avatar.source,
          quality_score: 0,
          issues: ['评估失败'],
        });
      }
    }

    return results;
  }

  /**
   * 选择最佳头像
   */
  selectBestAvatar(results: AvatarQualityResult[]): AvatarQualityResult | null {
    if (results.length === 0) return null;

    return results
      .filter(r => r.quality_score > 0)
      .sort((a, b) => b.quality_score - a.quality_score)[0] || null;
  }
}
