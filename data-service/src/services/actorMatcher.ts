import { Pool } from 'pg';
import { logger } from '../utils/logger';

export interface ActorMatchResult {
  actor_id: string;
  confidence: number;
  match_type: 'exact' | 'fuzzy' | 'alias' | 'id_match';
  matched_fields: string[];
  source_data: any;
}

export interface MatchingOptions {
  minConfidence?: number;
  enableFuzzyMatch?: boolean;
  enableAliasMatch?: boolean;
  maxResults?: number;
}

export class ActorMatcher {
  private db: Pool;
  private readonly DEFAULT_MIN_CONFIDENCE = 70;

  constructor(db: Pool) {
    this.db = db;
  }

  /**
   * 智能匹配演员信息
   */
  async matchActor(
    sourceData: any,
    options: MatchingOptions = {}
  ): Promise<ActorMatchResult[]> {
    const {
      minConfidence = this.DEFAULT_MIN_CONFIDENCE,
      enableFuzzyMatch = true,
      enableAliasMatch = true,
      maxResults = 5
    } = options;

    const results: ActorMatchResult[] = [];

    try {
      // 1. 精确ID匹配
      if (sourceData.actor_id) {
        const idMatch = await this.exactIdMatch(sourceData);
        if (idMatch) {
          results.push(idMatch);
        }
      }

      // 2. 精确姓名匹配
      if (sourceData.name) {
        const nameMatches = await this.exactNameMatch(sourceData);
        results.push(...nameMatches);
      }

      // 3. 模糊匹配（如果启用）
      if (enableFuzzyMatch && sourceData.name) {
        const fuzzyMatches = await this.fuzzyNameMatch(sourceData);
        results.push(...fuzzyMatches);
      }

      // 4. 别名匹配（如果启用）
      if (enableAliasMatch) {
        const aliasMatches = await this.aliasMatch(sourceData);
        results.push(...aliasMatches);
      }

      // 去重并按置信度排序
      const uniqueResults = this.deduplicateResults(results);
      const filteredResults = uniqueResults.filter(r => r.confidence >= minConfidence);
      
      return filteredResults
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, maxResults);

    } catch (error) {
      logger.error('Actor matching failed:', error);
      throw error;
    }
  }

  /**
   * 精确ID匹配
   */
  private async exactIdMatch(sourceData: any): Promise<ActorMatchResult | null> {
    try {
      const result = await this.db.query(
        'SELECT * FROM actors WHERE actor_id = $1',
        [sourceData.actor_id]
      );

      if (result.rows.length > 0) {
        const actor = result.rows[0];
        return {
          actor_id: actor.actor_id,
          confidence: 100,
          match_type: 'id_match',
          matched_fields: ['actor_id'],
          source_data: sourceData
        };
      }

      return null;
    } catch (error) {
      logger.error('Exact ID match failed:', error);
      return null;
    }
  }

  /**
   * 精确姓名匹配
   */
  private async exactNameMatch(sourceData: any): Promise<ActorMatchResult[]> {
    try {
      const results: ActorMatchResult[] = [];
      const names = [sourceData.name, sourceData.japanese_name, sourceData.english_name]
        .filter(Boolean);

      for (const name of names) {
        const result = await this.db.query(`
          SELECT * FROM actors 
          WHERE name = $1 OR japanese_name = $1 OR english_name = $1
        `, [name]);

        for (const actor of result.rows) {
          const matchedFields = this.getMatchedFields(actor, sourceData);
          const confidence = this.calculateConfidence(actor, sourceData, matchedFields);

          results.push({
            actor_id: actor.actor_id,
            confidence,
            match_type: 'exact',
            matched_fields: matchedFields,
            source_data: sourceData
          });
        }
      }

      return results;
    } catch (error) {
      logger.error('Exact name match failed:', error);
      return [];
    }
  }

  /**
   * 模糊姓名匹配
   */
  private async fuzzyNameMatch(sourceData: any): Promise<ActorMatchResult[]> {
    try {
      const results: ActorMatchResult[] = [];
      const name = sourceData.name;

      // 使用PostgreSQL的相似度函数
      const result = await this.db.query(`
        SELECT *, 
               GREATEST(
                 similarity(name, $1),
                 similarity(japanese_name, $1),
                 similarity(english_name, $1)
               ) as similarity_score
        FROM actors 
        WHERE 
          similarity(name, $1) > 0.3 OR
          similarity(japanese_name, $1) > 0.3 OR
          similarity(english_name, $1) > 0.3
        ORDER BY similarity_score DESC
        LIMIT 10
      `, [name]);

      for (const actor of result.rows) {
        const matchedFields = this.getMatchedFields(actor, sourceData);
        const baseConfidence = Math.round(actor.similarity_score * 100);
        const confidence = Math.min(baseConfidence + this.calculateBonusScore(actor, sourceData), 95);

        if (confidence >= 50) { // 模糊匹配的最低阈值
          results.push({
            actor_id: actor.actor_id,
            confidence,
            match_type: 'fuzzy',
            matched_fields: matchedFields,
            source_data: sourceData
          });
        }
      }

      return results;
    } catch (error) {
      logger.error('Fuzzy name match failed:', error);
      return [];
    }
  }

  /**
   * 别名匹配
   */
  private async aliasMatch(sourceData: any): Promise<ActorMatchResult[]> {
    try {
      const results: ActorMatchResult[] = [];
      
      // 这里可以实现基于已知别名数据库的匹配
      // 暂时返回空数组，后续可以扩展
      
      return results;
    } catch (error) {
      logger.error('Alias match failed:', error);
      return [];
    }
  }

  /**
   * 获取匹配的字段
   */
  private getMatchedFields(actor: any, sourceData: any): string[] {
    const matchedFields: string[] = [];
    
    const fieldsToCheck = [
      'name', 'japanese_name', 'english_name', 'birthday', 
      'height', 'bust', 'waist', 'hip', 'birthplace'
    ];

    for (const field of fieldsToCheck) {
      if (actor[field] && sourceData[field] && 
          this.normalizeValue(actor[field]) === this.normalizeValue(sourceData[field])) {
        matchedFields.push(field);
      }
    }

    return matchedFields;
  }

  /**
   * 计算匹配置信度
   */
  private calculateConfidence(actor: any, sourceData: any, matchedFields: string[]): number {
    let confidence = 0;

    // 基础分数：姓名匹配
    if (matchedFields.includes('name')) confidence += 40;
    if (matchedFields.includes('japanese_name')) confidence += 35;
    if (matchedFields.includes('english_name')) confidence += 30;

    // 额外匹配字段加分
    const bonusFields = ['birthday', 'height', 'bust', 'waist', 'hip', 'birthplace'];
    for (const field of bonusFields) {
      if (matchedFields.includes(field)) {
        confidence += field === 'birthday' ? 15 : 5;
      }
    }

    return Math.min(confidence, 100);
  }

  /**
   * 计算额外加分
   */
  private calculateBonusScore(actor: any, sourceData: any): number {
    let bonus = 0;

    // 生日匹配加分
    if (actor.birthday && sourceData.birthday && 
        this.normalizeDate(actor.birthday) === this.normalizeDate(sourceData.birthday)) {
      bonus += 20;
    }

    // 身体数据匹配加分
    if (actor.height && sourceData.height && 
        Math.abs(actor.height - sourceData.height) <= 2) {
      bonus += 10;
    }

    return bonus;
  }

  /**
   * 去重结果
   */
  private deduplicateResults(results: ActorMatchResult[]): ActorMatchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      if (seen.has(result.actor_id)) {
        return false;
      }
      seen.add(result.actor_id);
      return true;
    });
  }

  /**
   * 标准化值
   */
  private normalizeValue(value: any): string {
    if (value === null || value === undefined) return '';
    return String(value).trim().toLowerCase();
  }

  /**
   * 标准化日期
   */
  private normalizeDate(date: any): string {
    if (!date) return '';
    try {
      return new Date(date).toISOString().split('T')[0];
    } catch {
      return String(date);
    }
  }

  /**
   * 批量匹配演员
   */
  async batchMatchActors(
    sourceDataList: any[],
    options: MatchingOptions = {}
  ): Promise<Map<string, ActorMatchResult[]>> {
    const results = new Map<string, ActorMatchResult[]>();

    for (const sourceData of sourceDataList) {
      try {
        const matches = await this.matchActor(sourceData, options);
        const key = sourceData.actor_id || sourceData.name || 'unknown';
        results.set(key, matches);
      } catch (error) {
        logger.error(`Batch match failed for actor:`, error);
        results.set(sourceData.actor_id || sourceData.name || 'unknown', []);
      }
    }

    return results;
  }
}
