import { Pool } from 'pg';
import { createClient, RedisClientType } from 'redis';
import { logger } from '../utils/logger';

export interface ConfigItem {
  id: string;
  config_key: string;
  config_value: string;
  description?: string;
  service_name?: string;
  config_type: 'general' | 'service' | 'feature' | 'performance';
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface ConfigFilter {
  service_name?: string;
  config_type?: string;
  is_active?: boolean;
}

export class ConfigManager {
  private db: Pool;
  private redis: RedisClientType;
  private readonly CACHE_PREFIX = 'config:';
  private readonly CACHE_TTL = 3600; // 1小时
  private readonly NOTIFICATION_CHANNEL = 'config_updates';

  constructor(db: Pool, redis: RedisClientType) {
    this.db = db;
    this.redis = redis;
    this.setupNotificationListener();
  }

  /**
   * 获取配置项
   */
  async getConfig(key: string, useCache: boolean = true): Promise<ConfigItem | null> {
    try {
      // 尝试从缓存获取
      if (useCache) {
        const cached = await this.redis.get(`${this.CACHE_PREFIX}${key}`);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      // 从数据库获取
      const result = await this.db.query(
        'SELECT * FROM system_config WHERE config_key = $1 AND is_active = true',
        [key]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const config = result.rows[0];
      
      // 缓存结果
      if (useCache) {
        await this.redis.setEx(
          `${this.CACHE_PREFIX}${key}`,
          this.CACHE_TTL,
          JSON.stringify(config)
        );
      }

      return config;
    } catch (error) {
      logger.error('Failed to get config:', error);
      throw error;
    }
  }

  /**
   * 获取多个配置项
   */
  async getConfigs(filter: ConfigFilter = {}): Promise<ConfigItem[]> {
    try {
      let query = 'SELECT * FROM system_config WHERE is_active = true';
      const params: any[] = [];
      let paramIndex = 1;

      if (filter.service_name) {
        query += ` AND service_name = $${paramIndex}`;
        params.push(filter.service_name);
        paramIndex++;
      }

      if (filter.config_type) {
        query += ` AND config_type = $${paramIndex}`;
        params.push(filter.config_type);
        paramIndex++;
      }

      query += ' ORDER BY service_name, config_key';

      const result = await this.db.query(query, params);
      return result.rows;
    } catch (error) {
      logger.error('Failed to get configs:', error);
      throw error;
    }
  }

  /**
   * 设置配置项
   */
  async setConfig(
    key: string,
    value: string,
    options: {
      description?: string;
      service_name?: string;
      config_type?: 'general' | 'service' | 'feature' | 'performance';
      is_active?: boolean;
    } = {}
  ): Promise<ConfigItem> {
    try {
      const {
        description,
        service_name,
        config_type = 'general',
        is_active = true
      } = options;

      const result = await this.db.query(`
        INSERT INTO system_config (config_key, config_value, description, service_name, config_type, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (config_key)
        DO UPDATE SET 
          config_value = EXCLUDED.config_value,
          description = EXCLUDED.description,
          service_name = EXCLUDED.service_name,
          config_type = EXCLUDED.config_type,
          is_active = EXCLUDED.is_active,
          updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `, [key, value, description, service_name, config_type, is_active]);

      const config = result.rows[0];

      // 清除缓存
      await this.redis.del(`${this.CACHE_PREFIX}${key}`);

      // 发送配置更新通知
      await this.notifyConfigUpdate(config);

      logger.info(`Config updated: ${key} = ${value}`);
      return config;
    } catch (error) {
      logger.error('Failed to set config:', error);
      throw error;
    }
  }

  /**
   * 删除配置项
   */
  async deleteConfig(key: string): Promise<boolean> {
    try {
      const result = await this.db.query(
        'DELETE FROM system_config WHERE config_key = $1',
        [key]
      );

      if (result.rowCount && result.rowCount > 0) {
        // 清除缓存
        await this.redis.del(`${this.CACHE_PREFIX}${key}`);

        // 发送删除通知
        await this.notifyConfigDelete(key);

        logger.info(`Config deleted: ${key}`);
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Failed to delete config:', error);
      throw error;
    }
  }

  /**
   * 批量设置配置项
   */
  async setConfigs(configs: Array<{
    key: string;
    value: string;
    description?: string;
    service_name?: string;
    config_type?: 'general' | 'service' | 'feature' | 'performance';
    is_active?: boolean;
  }>): Promise<ConfigItem[]> {
    try {
      const results: ConfigItem[] = [];

      for (const config of configs) {
        const result = await this.setConfig(config.key, config.value, {
          description: config.description,
          service_name: config.service_name,
          config_type: config.config_type,
          is_active: config.is_active
        });
        results.push(result);
      }

      return results;
    } catch (error) {
      logger.error('Failed to set configs:', error);
      throw error;
    }
  }

  /**
   * 获取服务配置
   */
  async getServiceConfig(serviceName: string): Promise<Record<string, string>> {
    try {
      const configs = await this.getConfigs({ service_name: serviceName });
      return configs.reduce((acc, config) => {
        acc[config.config_key] = config.config_value;
        return acc;
      }, {} as Record<string, string>);
    } catch (error) {
      logger.error('Failed to get service config:', error);
      throw error;
    }
  }

  /**
   * 发送配置更新通知
   */
  private async notifyConfigUpdate(config: ConfigItem): Promise<void> {
    try {
      const notification = {
        type: 'config_update',
        config_key: config.config_key,
        config_value: config.config_value,
        service_name: config.service_name,
        timestamp: new Date().toISOString()
      };

      await this.redis.publish(this.NOTIFICATION_CHANNEL, JSON.stringify(notification));
    } catch (error) {
      logger.error('Failed to send config update notification:', error);
    }
  }

  /**
   * 发送配置删除通知
   */
  private async notifyConfigDelete(key: string): Promise<void> {
    try {
      const notification = {
        type: 'config_delete',
        config_key: key,
        timestamp: new Date().toISOString()
      };

      await this.redis.publish(this.NOTIFICATION_CHANNEL, JSON.stringify(notification));
    } catch (error) {
      logger.error('Failed to send config delete notification:', error);
    }
  }

  /**
   * 设置通知监听器
   */
  private setupNotificationListener(): void {
    // 这里可以添加监听其他服务的配置变更请求
    logger.info('Config notification listener setup completed');
  }

  /**
   * 清除所有配置缓存
   */
  async clearCache(): Promise<void> {
    try {
      const keys = await this.redis.keys(`${this.CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await this.redis.del(keys);
      }
      logger.info('Config cache cleared');
    } catch (error) {
      logger.error('Failed to clear config cache:', error);
      throw error;
    }
  }

  /**
   * 获取配置统计信息
   */
  async getConfigStats(): Promise<{
    total: number;
    by_service: Record<string, number>;
    by_type: Record<string, number>;
    active: number;
    inactive: number;
  }> {
    try {
      const result = await this.db.query(`
        SELECT
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE is_active = true) as active,
          COUNT(*) FILTER (WHERE is_active = false) as inactive,
          json_object_agg(
            COALESCE(service_name, 'general'),
            service_count
          ) as by_service,
          json_object_agg(config_type, type_count) as by_type
        FROM (
          SELECT
            service_name,
            config_type,
            is_active,
            COUNT(*) OVER (PARTITION BY COALESCE(service_name, 'general')) as service_count,
            COUNT(*) OVER (PARTITION BY config_type) as type_count
          FROM system_config
        ) stats
      `);

      const stats = result.rows[0];
      return {
        total: parseInt(stats.total),
        active: parseInt(stats.active),
        inactive: parseInt(stats.inactive),
        by_service: stats.by_service || {},
        by_type: stats.by_type || {}
      };
    } catch (error) {
      logger.error('Failed to get config stats:', error);
      throw error;
    }
  }
}
