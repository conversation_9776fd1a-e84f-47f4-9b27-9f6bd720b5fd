import axios from 'axios';
import { RedisClientType } from 'redis';
import { logger } from '../utils/logger';

export interface ServiceInfo {
  name: string;
  url: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  version?: string;
  lastCheck: Date;
  responseTime?: number;
  metadata?: Record<string, any>;
}

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy';
  responseTime: number;
  error?: string;
  metadata?: Record<string, any>;
}

export class ServiceDiscovery {
  private redis: RedisClientType;
  private services: Map<string, ServiceInfo> = new Map();
  private readonly CACHE_PREFIX = 'service:';
  private readonly CACHE_TTL = 300; // 5分钟
  private readonly CHECK_INTERVAL = 30000; // 30秒
  private checkTimer?: NodeJS.Timeout;

  constructor(redis: RedisClientType) {
    this.redis = redis;
    this.initializeServices();
    this.startHealthChecks();
  }

  /**
   * 初始化服务列表
   */
  private initializeServices(): void {
    const defaultServices = [
      {
        name: 'javbus-api',
        url: process.env.JAVBUS_API_URL || 'http://javbus-api:3000',
        status: 'unknown' as const,
        lastCheck: new Date()
      },
      {
        name: 'javsp-api',
        url: process.env.JAVSP_API_URL || 'http://javsp:8000',
        status: 'unknown' as const,
        lastCheck: new Date()
      },
      {
        name: 'javinizer',
        url: process.env.JAVINIZER_API_URL || 'http://javinizer:5000',
        status: 'unknown' as const,
        lastCheck: new Date()
      },
      {
        name: 'data-service',
        url: process.env.DATA_SERVICE_URL || 'http://data-service:3002',
        status: 'unknown' as const,
        lastCheck: new Date()
      }
    ];

    defaultServices.forEach(service => {
      this.services.set(service.name, service);
    });

    logger.info(`Initialized ${defaultServices.length} services for discovery`);
  }

  /**
   * 注册服务
   */
  async registerService(serviceInfo: Omit<ServiceInfo, 'lastCheck'>): Promise<void> {
    try {
      const service: ServiceInfo = {
        ...serviceInfo,
        lastCheck: new Date()
      };

      this.services.set(service.name, service);
      
      // 缓存到Redis
      await this.redis.setEx(
        `${this.CACHE_PREFIX}${service.name}`,
        this.CACHE_TTL,
        JSON.stringify(service)
      );

      logger.info(`Service registered: ${service.name} at ${service.url}`);
    } catch (error) {
      logger.error('Failed to register service:', error);
      throw error;
    }
  }

  /**
   * 注销服务
   */
  async unregisterService(serviceName: string): Promise<boolean> {
    try {
      const removed = this.services.delete(serviceName);
      
      if (removed) {
        await this.redis.del(`${this.CACHE_PREFIX}${serviceName}`);
        logger.info(`Service unregistered: ${serviceName}`);
      }

      return removed;
    } catch (error) {
      logger.error('Failed to unregister service:', error);
      throw error;
    }
  }

  /**
   * 获取服务信息
   */
  async getService(serviceName: string): Promise<ServiceInfo | null> {
    try {
      // 先从内存获取
      let service = this.services.get(serviceName);
      
      if (!service) {
        // 从Redis获取
        const cached = await this.redis.get(`${this.CACHE_PREFIX}${serviceName}`);
        if (cached) {
          service = JSON.parse(cached);
          if (service) {
            this.services.set(serviceName, service);
          }
        }
      }

      return service || null;
    } catch (error) {
      logger.error('Failed to get service:', error);
      throw error;
    }
  }

  /**
   * 获取所有服务
   */
  async getAllServices(): Promise<ServiceInfo[]> {
    try {
      return Array.from(this.services.values());
    } catch (error) {
      logger.error('Failed to get all services:', error);
      throw error;
    }
  }

  /**
   * 获取健康的服务
   */
  async getHealthyServices(): Promise<ServiceInfo[]> {
    try {
      return Array.from(this.services.values()).filter(
        service => service.status === 'healthy'
      );
    } catch (error) {
      logger.error('Failed to get healthy services:', error);
      throw error;
    }
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck(serviceName: string): Promise<HealthCheckResult> {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service not found: ${serviceName}`);
    }

    const startTime = Date.now();
    
    try {
      const response = await axios.get(`${service.url}/health`, {
        timeout: 5000,
        validateStatus: (status) => status < 500
      });

      const responseTime = Date.now() - startTime;
      const isHealthy = response.status >= 200 && response.status < 400;

      const result: HealthCheckResult = {
        service: serviceName,
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        metadata: response.data
      };

      // 更新服务状态
      service.status = result.status;
      service.lastCheck = new Date();
      service.responseTime = responseTime;
      service.metadata = result.metadata;

      // 更新缓存
      await this.redis.setEx(
        `${this.CACHE_PREFIX}${serviceName}`,
        this.CACHE_TTL,
        JSON.stringify(service)
      );

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      const result: HealthCheckResult = {
        service: serviceName,
        status: 'unhealthy',
        responseTime,
        error: errorMessage
      };

      // 更新服务状态
      service.status = 'unhealthy';
      service.lastCheck = new Date();
      service.responseTime = responseTime;

      return result;
    }
  }

  /**
   * 执行所有服务的健康检查
   */
  async performAllHealthChecks(): Promise<HealthCheckResult[]> {
    try {
      const serviceNames = Array.from(this.services.keys());
      const results = await Promise.allSettled(
        serviceNames.map(name => this.performHealthCheck(name))
      );

      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            service: serviceNames[index],
            status: 'unhealthy' as const,
            responseTime: 0,
            error: result.reason?.message || 'Health check failed'
          };
        }
      });
    } catch (error) {
      logger.error('Failed to perform all health checks:', error);
      throw error;
    }
  }

  /**
   * 启动定期健康检查
   */
  private startHealthChecks(): void {
    this.checkTimer = setInterval(async () => {
      try {
        await this.performAllHealthChecks();
        logger.debug('Periodic health checks completed');
      } catch (error) {
        logger.error('Periodic health check failed:', error);
      }
    }, this.CHECK_INTERVAL);

    logger.info('Started periodic health checks');
  }

  /**
   * 停止健康检查
   */
  stopHealthChecks(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = undefined;
      logger.info('Stopped periodic health checks');
    }
  }

  /**
   * 获取服务统计信息
   */
  async getServiceStats(): Promise<{
    total: number;
    healthy: number;
    unhealthy: number;
    unknown: number;
    averageResponseTime: number;
  }> {
    try {
      const services = Array.from(this.services.values());
      const total = services.length;
      const healthy = services.filter(s => s.status === 'healthy').length;
      const unhealthy = services.filter(s => s.status === 'unhealthy').length;
      const unknown = services.filter(s => s.status === 'unknown').length;
      
      const responseTimes = services
        .filter(s => s.responseTime !== undefined)
        .map(s => s.responseTime!);
      
      const averageResponseTime = responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;

      return {
        total,
        healthy,
        unhealthy,
        unknown,
        averageResponseTime: Math.round(averageResponseTime)
      };
    } catch (error) {
      logger.error('Failed to get service stats:', error);
      throw error;
    }
  }
}
