import { Request, Response, NextFunction } from 'express';
import { redis } from '../server';
import { logger } from '../utils/logger';

export const cacheMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  // 只缓存GET请求
  if (req.method !== 'GET') {
    return next();
  }
  
  // 跳过某些路径
  const skipPaths = ['/health', '/api/admin'];
  if (skipPaths.some(path => req.path.startsWith(path))) {
    return next();
  }
  
  try {
    const cacheKey = `cache:${req.originalUrl}`;
    const cachedData = await redis.get(cacheKey);
    
    if (cachedData) {
      logger.info(`Cache hit for ${req.originalUrl}`);
      res.setHeader('X-Cache', 'HIT');
      return res.json(JSON.parse(cachedData));
    }
    
    // 重写res.json方法来缓存响应
    const originalJson = res.json;
    res.json = function(data: any) {
      // 缓存成功响应（状态码200-299）
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const ttl = getCacheTTL(req.path);
        redis.setEx(cacheKey, ttl, JSON.stringify(data)).catch((err: any) => {
          logger.error('Cache set error:', err);
        });
      }
      
      res.setHeader('X-Cache', 'MISS');
      return originalJson.call(this, data);
    };
    
    next();
  } catch (error) {
    logger.error('Cache middleware error:', error);
    next();
  }
};

function getCacheTTL(path: string): number {
  // 根据路径设置不同的缓存时间
  if (path.includes('/movies/')) {
    return 3600; // 1小时
  } else if (path.includes('/actors/')) {
    return 7200; // 2小时
  } else if (path.includes('/search')) {
    return 1800; // 30分钟
  }
  
  return 1800; // 默认30分钟
}

export const clearCache = async (pattern: string = '*') => {
  try {
    const keys = await redis.keys(`cache:${pattern}`);
    if (keys.length > 0) {
      await redis.del(keys);
      logger.info(`Cleared ${keys.length} cache entries`);
    }
  } catch (error) {
    logger.error('Clear cache error:', error);
  }
};
