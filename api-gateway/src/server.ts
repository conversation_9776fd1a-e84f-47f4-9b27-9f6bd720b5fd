import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { createClient } from 'redis';
import { Pool } from 'pg';
import dotenv from 'dotenv';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { cacheMiddleware } from './middleware/cache';

// 路由导入
import moviesRouter from './routes/movies';
import actorsRouter from './routes/actors';
import searchRouter from './routes/search';
import dataRouter from './routes/data';
import adminRouter from './routes/admin';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 8080;

// 数据库连接
export const db = new Pool({
  connectionString: process.env.DATABASE_URL || '*********************************************/javdb',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Redis连接
export const redis = createClient({
  url: process.env.REDIS_URL || 'redis://redis:6379'
});

// 中间件配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
}));

app.use(compression());
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000次请求
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 缓存中间件
app.use(cacheMiddleware);

// 健康检查
app.get('/health', async (req, res) => {
  try {
    // 检查数据库连接
    await db.query('SELECT 1');
    
    // 检查Redis连接
    await redis.ping();
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        redis: 'connected',
        javbus_api: 'unknown',
        javsp_api: 'unknown'
      }
    });
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: (error as Error).message
    });
  }
});

// API路由
app.use('/api/movies', moviesRouter);
app.use('/api/actors', actorsRouter);
app.use('/api/search', searchRouter);
app.use('/api/data', dataRouter);
app.use('/api/admin', authMiddleware, adminRouter);

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'JAV API Gateway',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      movies: '/api/movies',
      actors: '/api/actors',
      search: '/api/search',
      data: '/api/data',
      admin: '/api/admin',
      health: '/health'
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
async function startServer() {
  try {
    // 连接Redis
    await redis.connect();
    logger.info('Redis connected successfully');
    
    // 测试数据库连接
    await db.query('SELECT NOW()');
    logger.info('Database connected successfully');
    
    // 启动HTTP服务器
    app.listen(PORT, () => {
      logger.info(`API Gateway server is running on port ${PORT}`);
    });
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  try {
    await redis.quit();
    await db.end();
    logger.info('Connections closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  
  try {
    await redis.quit();
    await db.end();
    logger.info('Connections closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// 启动服务器
startServer();

export default app;
