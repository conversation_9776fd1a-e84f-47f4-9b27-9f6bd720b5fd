import { Router } from 'express';
import axios from 'axios';
import { db } from '../server';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';

const router = Router();

const JAVBUS_API_URL = process.env.JAVBUS_API_URL || 'http://javbus-api:3000';

// 获取演员列表
router.get('/', async (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    
    const result = await db.query(`
      SELECT 
        a.*,
        COUNT(ma.movie_id) as movie_count
      FROM actors a
      LEFT JOIN movie_actors ma ON a.id = ma.actor_id
      GROUP BY a.id
      ORDER BY movie_count DESC, a.name
      LIMIT $1 OFFSET $2
    `, [limit, offset]);
    
    const countResult = await db.query('SELECT COUNT(*) as total FROM actors');
    const total = parseInt(countResult.rows[0].total);
    
    res.json({
      actors: result.rows,
      pagination: {
        currentPage: Number(page),
        totalPages: Math.ceil(total / Number(limit)),
        totalItems: total,
        hasNextPage: offset + Number(limit) < total
      }
    });
  } catch (error) {
    logger.error('Get actors error:', error);
    next(createError('Failed to fetch actors', 500));
  }
});

// 获取演员详情
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { source = 'auto' } = req.query;
    
    let actorData = null;
    
    // 从数据库获取
    if (source === 'database' || source === 'auto') {
      const result = await db.query(`
        SELECT 
          a.*,
          COALESCE(
            json_agg(
              json_build_object(
                'id', m.movie_id,
                'title', m.title,
                'cover', m.cover_local_path,
                'release_date', m.release_date
              )
            ) FILTER (WHERE m.id IS NOT NULL), 
            '[]'
          ) as movies
        FROM actors a
        LEFT JOIN movie_actors ma ON a.id = ma.actor_id
        LEFT JOIN movies m ON ma.movie_id = m.id
        WHERE a.actor_id = $1
        GROUP BY a.id
      `, [id]);
      
      if (result.rows.length > 0) {
        actorData = result.rows[0];
      }
    }
    
    // 从API获取
    if (!actorData && (source === 'api' || source === 'auto')) {
      try {
        const response = await axios.get(`${JAVBUS_API_URL}/api/stars/${id}`, {
          timeout: 10000
        });
        actorData = response.data;
      } catch (apiError) {
        logger.warn(`JavBus API error for actor ${id}:`, (apiError as Error).message);
      }
    }
    
    if (!actorData) {
      return next(createError('Actor not found', 404));
    }
    
    res.json(actorData);
  } catch (error) {
    logger.error(`Get actor ${req.params.id} error:`, error);
    next(createError('Failed to fetch actor details', 500));
  }
});

export default router;
