import { Router } from 'express';
import axios from 'axios';
import { db } from '../server';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';

const router = Router();

// 服务URL配置
const JAVBUS_API_URL = process.env.JAVBUS_API_URL || 'http://javbus-api:3000';
const JAVSP_API_URL = process.env.JAVSP_API_URL || 'http://javsp:8000';

// 获取影片列表
router.get('/', async (req, res, next) => {
  try {
    const { page = 1, limit = 20, source = 'database' } = req.query;
    
    if (source === 'database') {
      // 从数据库获取
      const offset = (Number(page) - 1) * Number(limit);
      const result = await db.query(`
        SELECT 
          m.*,
          COALESCE(
            json_agg(
              json_build_object('id', a.actor_id, 'name', a.name, 'avatar', a.avatar_local_path)
            ) FILTER (WHERE a.id IS NOT NULL), 
            '[]'
          ) as actors,
          COALESCE(
            json_agg(
              json_build_object('id', g.genre_id, 'name', g.name)
            ) FILTER (WHERE g.id IS NOT NULL), 
            '[]'
          ) as genres
        FROM movies m
        LEFT JOIN movie_actors ma ON m.id = ma.movie_id
        LEFT JOIN actors a ON ma.actor_id = a.id
        LEFT JOIN movie_genres mg ON m.id = mg.movie_id
        LEFT JOIN genres g ON mg.genre_id = g.id
        GROUP BY m.id
        ORDER BY m.release_date DESC, m.created_at DESC
        LIMIT $1 OFFSET $2
      `, [limit, offset]);
      
      // 获取总数
      const countResult = await db.query('SELECT COUNT(*) as total FROM movies');
      const total = parseInt(countResult.rows[0].total);
      
      res.json({
        movies: result.rows,
        pagination: {
          currentPage: Number(page),
          totalPages: Math.ceil(total / Number(limit)),
          totalItems: total,
          hasNextPage: offset + Number(limit) < total
        }
      });
    } else {
      // 从JavBus API获取
      const response = await axios.get(`${JAVBUS_API_URL}/api/movies`, {
        params: req.query,
        timeout: 10000
      });
      res.json(response.data);
    }
  } catch (error) {
    logger.error('Get movies error:', error);
    next(createError('Failed to fetch movies', 500));
  }
});

// 获取影片详情
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { source = 'auto' } = req.query;
    
    let movieData = null;
    
    // 优先从数据库获取
    if (source === 'database' || source === 'auto') {
      const result = await db.query(`
        SELECT 
          m.*,
          COALESCE(
            json_agg(
              json_build_object('id', a.actor_id, 'name', a.name, 'avatar', a.avatar_local_path)
            ) FILTER (WHERE a.id IS NOT NULL), 
            '[]'
          ) as actors,
          COALESCE(
            json_agg(DISTINCT
              json_build_object('id', g.genre_id, 'name', g.name)
            ) FILTER (WHERE g.id IS NOT NULL), 
            '[]'
          ) as genres,
          COALESCE(
            json_agg(DISTINCT
              json_build_object(
                'id', mag.magnet_id, 
                'link', mag.link, 
                'title', mag.title,
                'size', mag.size_text,
                'isHD', mag.is_hd,
                'hasSubtitle', mag.has_subtitle
              )
            ) FILTER (WHERE mag.id IS NOT NULL), 
            '[]'
          ) as magnets,
          COALESCE(
            json_agg(DISTINCT
              json_build_object(
                'id', si.image_id,
                'src', si.url,
                'thumbnail', si.thumbnail_url,
                'alt', si.alt_text
              )
            ) FILTER (WHERE si.id IS NOT NULL), 
            '[]'
          ) as samples
        FROM movies m
        LEFT JOIN movie_actors ma ON m.id = ma.movie_id
        LEFT JOIN actors a ON ma.actor_id = a.id
        LEFT JOIN movie_genres mg ON m.id = mg.movie_id
        LEFT JOIN genres g ON mg.genre_id = g.id
        LEFT JOIN magnets mag ON m.id = mag.movie_id
        LEFT JOIN sample_images si ON m.id = si.movie_id
        WHERE m.movie_id = $1
        GROUP BY m.id
      `, [id]);
      
      if (result.rows.length > 0) {
        movieData = result.rows[0];
      }
    }
    
    // 如果数据库中没有或者指定从API获取
    if (!movieData && (source === 'api' || source === 'auto')) {
      try {
        const response = await axios.get(`${JAVBUS_API_URL}/api/movies/${id}`, {
          timeout: 10000
        });
        movieData = response.data;
      } catch (apiError: any) {
        logger.warn(`JavBus API error for ${id}:`, apiError.message);
      }
    }
    
    if (!movieData) {
      return next(createError('Movie not found', 404));
    }
    
    res.json(movieData);
  } catch (error) {
    logger.error(`Get movie ${req.params.id} error:`, error);
    next(createError('Failed to fetch movie details', 500));
  }
});

// 获取影片磁力链接
router.get('/:id/magnets', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { source = 'database' } = req.query;
    
    if (source === 'database') {
      const result = await db.query(`
        SELECT 
          mag.magnet_id as id,
          mag.link,
          mag.title,
          mag.size_text as size,
          mag.size_bytes as numberSize,
          mag.share_date as shareDate,
          mag.is_hd as isHD,
          mag.has_subtitle as hasSubtitle
        FROM magnets mag
        JOIN movies m ON mag.movie_id = m.id
        WHERE m.movie_id = $1
        ORDER BY mag.size_bytes DESC, mag.share_date DESC
      `, [id]);
      
      res.json(result.rows);
    } else {
      // 从JavBus API获取
      const response = await axios.get(`${JAVBUS_API_URL}/api/magnets/${id}`, {
        params: req.query,
        timeout: 10000
      });
      res.json(response.data);
    }
  } catch (error) {
    logger.error(`Get magnets for ${req.params.id} error:`, error);
    next(createError('Failed to fetch magnets', 500));
  }
});

// 触发数据抓取
router.post('/:id/scrape', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { sources = ['javbus', 'javsp'] } = req.body;
    
    const results = [];
    
    // 从JavBus抓取
    if (sources.includes('javbus')) {
      try {
        const response = await axios.get(`${JAVBUS_API_URL}/api/movies/${id}`, {
          timeout: 15000
        });
        results.push({
          source: 'javbus',
          status: 'success',
          data: response.data
        });
      } catch (error) {
        results.push({
          source: 'javbus',
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    // 从JavSP抓取
    if (sources.includes('javsp')) {
      try {
        const response = await axios.post(`${JAVSP_API_URL}/scrape`, {
          movie_id: id
        }, {
          timeout: 30000
        });
        results.push({
          source: 'javsp',
          status: 'success',
          data: response.data
        });
      } catch (error) {
        results.push({
          source: 'javsp',
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    res.json({
      movie_id: id,
      results
    });
  } catch (error) {
    logger.error(`Scrape movie ${req.params.id} error:`, error);
    next(createError('Failed to scrape movie data', 500));
  }
});

export default router;
