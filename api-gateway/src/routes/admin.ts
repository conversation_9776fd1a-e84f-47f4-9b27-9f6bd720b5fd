import { Router } from 'express';
import axios from 'axios';
import { db, redis } from '../server';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import { clearCache } from '../middleware/cache';
import { ConfigManager } from '../services/configManager';
import { ServiceDiscovery } from '../services/serviceDiscovery';

const router = Router();

const JAVBUS_API_URL = process.env.JAVBUS_API_URL || 'http://javbus-api:3000';
const JAVSP_API_URL = process.env.JAVSP_API_URL || 'http://javsp:8000';

// 初始化配置管理和服务发现
const configManager = new ConfigManager(db, redis);
const serviceDiscovery = new ServiceDiscovery(redis);

// 系统健康检查（增强版）
router.get('/health', async (req, res, next) => {
  try {
    // 执行所有服务的健康检查
    const healthResults = await serviceDiscovery.performAllHealthChecks();

    // 检查数据库
    let databaseHealthy = false;
    try {
      await db.query('SELECT 1');
      databaseHealthy = true;
    } catch (error) {
      logger.error('Database health check failed:', error);
    }

    // 检查Redis
    let redisHealthy = false;
    try {
      await redis.ping();
      redisHealthy = true;
    } catch (error) {
      logger.error('Redis health check failed:', error);
    }

    // 构建响应
    const services = healthResults.reduce((acc, result) => {
      acc[result.service] = {
        status: result.status,
        responseTime: result.responseTime,
        error: result.error,
        metadata: result.metadata
      };
      return acc;
    }, {} as any);

    services.database = { status: databaseHealthy ? 'healthy' : 'unhealthy' };
    services.redis = { status: redisHealthy ? 'healthy' : 'unhealthy' };

    const allHealthy = databaseHealthy && redisHealthy &&
      healthResults.every(result => result.status === 'healthy');

    // 获取服务统计
    const serviceStats = await serviceDiscovery.getServiceStats();

    res.status(allHealthy ? 200 : 503).json({
      status: allHealthy ? 'healthy' : 'degraded',
      services,
      stats: serviceStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Admin health check error:', error);
    next(createError('Failed to perform health check', 500));
  }
});

// 批量数据操作
router.post('/batch/scrape', async (req, res, next) => {
  try {
    const { movie_ids, sources = ['javbus'] } = req.body;
    
    if (!Array.isArray(movie_ids) || movie_ids.length === 0) {
      return next(createError('movie_ids array is required', 400));
    }
    
    if (movie_ids.length > 100) {
      return next(createError('Maximum 100 movies per batch', 400));
    }
    
    const results = [];
    
    for (const movieId of movie_ids) {
      try {
        const movieResults = [];
        
        // 从各个源抓取数据
        for (const source of sources) {
          if (source === 'javbus') {
            try {
              const response = await axios.get(`${JAVBUS_API_URL}/api/movies/${movieId}`, {
                timeout: 15000
              });
              movieResults.push({
                source: 'javbus',
                status: 'success',
                data: response.data
              });
            } catch (error) {
              movieResults.push({
                source: 'javbus',
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }
          
          if (source === 'javsp') {
            try {
              const response = await axios.post(`${JAVSP_API_URL}/scrape`, {
                movie_id: movieId
              }, {
                timeout: 30000
              });
              movieResults.push({
                source: 'javsp',
                status: 'success',
                data: response.data
              });
            } catch (error) {
              movieResults.push({
                source: 'javsp',
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }
        }
        
        results.push({
          movie_id: movieId,
          results: movieResults
        });
        
      } catch (error) {
        results.push({
          movie_id: movieId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    res.json({
      batch_id: `batch_${Date.now()}`,
      total_movies: movie_ids.length,
      results
    });
  } catch (error) {
    logger.error('Batch scrape error:', error);
    next(createError('Failed to perform batch scrape', 500));
  }
});

// 数据库维护
router.post('/maintenance/cleanup', async (req, res, next) => {
  try {
    const { action } = req.body;
    
    let result: any = {};
    
    switch (action) {
      case 'remove_duplicates':
        // 移除重复的影片记录
        const duplicateMovies = await db.query(`
          DELETE FROM movies 
          WHERE id NOT IN (
            SELECT MIN(id) 
            FROM movies 
            GROUP BY movie_id
          )
        `);
        result.removed_duplicate_movies = duplicateMovies.rowCount;
        break;
        
      case 'clean_orphaned_actors':
        // 清理没有关联影片的演员
        const orphanedActors = await db.query(`
          DELETE FROM actors 
          WHERE id NOT IN (
            SELECT DISTINCT actor_id 
            FROM movie_actors 
            WHERE actor_id IS NOT NULL
          )
        `);
        result.removed_orphaned_actors = orphanedActors.rowCount;
        break;
        
      case 'update_stats':
        // 更新统计信息
        await db.query('ANALYZE');
        result.stats_updated = true;
        break;
        
      default:
        return next(createError('Invalid maintenance action', 400));
    }
    
    res.json({
      action,
      result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Maintenance cleanup error:', error);
    next(createError('Failed to perform maintenance', 500));
  }
});

// 缓存管理
router.post('/cache/manage', async (req, res, next) => {
  try {
    const { action, pattern = '*' } = req.body;
    
    switch (action) {
      case 'clear':
        await clearCache(pattern);
        res.json({ message: 'Cache cleared', pattern });
        break;
        
      case 'warm':
        // 预热缓存 - 获取热门数据
        const popularMovies = await db.query(`
          SELECT movie_id FROM movies 
          ORDER BY created_at DESC 
          LIMIT 50
        `);
        
        // 这里可以添加预热逻辑
        res.json({ 
          message: 'Cache warming initiated', 
          items: popularMovies.rows.length 
        });
        break;
        
      default:
        return next(createError('Invalid cache action', 400));
    }
  } catch (error) {
    logger.error('Cache management error:', error);
    next(createError('Failed to manage cache', 500));
  }
});

// 系统配置管理（增强版）
router.get('/config', async (req, res, next) => {
  try {
    const { service_name, config_type, format = 'object' } = req.query;

    const filter: any = {};
    if (service_name) filter.service_name = service_name as string;
    if (config_type) filter.config_type = config_type as string;

    const configs = await configManager.getConfigs(filter);

    if (format === 'array') {
      res.json({ configs });
    } else {
      // 返回对象格式（向后兼容）
      const configObject = configs.reduce((acc: any, config: any) => {
        acc[config.config_key] = config.config_value;
        return acc;
      }, {});

      res.json({
        config: configObject,
        metadata: {
          total: configs.length,
          services: [...new Set(configs.map(c => c.service_name).filter(Boolean))],
          types: [...new Set(configs.map(c => c.config_type))]
        }
      });
    }
  } catch (error) {
    logger.error('Get config error:', error);
    next(createError('Failed to fetch configuration', 500));
  }
});

router.post('/config', async (req, res, next) => {
  try {
    const { config_key, config_value, description, service_name, config_type, is_active } = req.body;

    if (!config_key || config_value === undefined) {
      return next(createError('config_key and config_value are required', 400));
    }

    const config = await configManager.setConfig(config_key, config_value, {
      description,
      service_name,
      config_type,
      is_active
    });

    res.json({
      message: 'Configuration updated',
      config
    });
  } catch (error) {
    logger.error('Update config error:', error);
    next(createError('Failed to update configuration', 500));
  }
});

// 获取特定配置项
router.get('/config/:key', async (req, res, next) => {
  try {
    const { key } = req.params;
    const config = await configManager.getConfig(key);

    if (!config) {
      return next(createError('Configuration not found', 404));
    }

    res.json({ config });
  } catch (error) {
    logger.error('Get config item error:', error);
    next(createError('Failed to fetch configuration item', 500));
  }
});

// 删除配置项
router.delete('/config/:key', async (req, res, next) => {
  try {
    const { key } = req.params;
    const deleted = await configManager.deleteConfig(key);

    if (!deleted) {
      return next(createError('Configuration not found', 404));
    }

    res.json({ message: 'Configuration deleted', config_key: key });
  } catch (error) {
    logger.error('Delete config error:', error);
    next(createError('Failed to delete configuration', 500));
  }
});

// 批量设置配置
router.post('/config/batch', async (req, res, next) => {
  try {
    const { configs } = req.body;

    if (!Array.isArray(configs) || configs.length === 0) {
      return next(createError('configs array is required', 400));
    }

    const results = await configManager.setConfigs(configs);

    res.json({
      message: 'Configurations updated',
      count: results.length,
      configs: results
    });
  } catch (error) {
    logger.error('Batch config update error:', error);
    next(createError('Failed to update configurations', 500));
  }
});

// 获取服务配置
router.get('/config/service/:serviceName', async (req, res, next) => {
  try {
    const { serviceName } = req.params;
    const config = await configManager.getServiceConfig(serviceName);

    res.json({
      service: serviceName,
      config
    });
  } catch (error) {
    logger.error('Get service config error:', error);
    next(createError('Failed to fetch service configuration', 500));
  }
});

// 配置统计信息
router.get('/config/stats', async (req, res, next) => {
  try {
    const stats = await configManager.getConfigStats();
    res.json({ stats });
  } catch (error) {
    logger.error('Get config stats error:', error);
    next(createError('Failed to fetch configuration statistics', 500));
  }
});

// 清除配置缓存
router.post('/config/cache/clear', async (req, res, next) => {
  try {
    await configManager.clearCache();
    res.json({ message: 'Configuration cache cleared' });
  } catch (error) {
    logger.error('Clear config cache error:', error);
    next(createError('Failed to clear configuration cache', 500));
  }
});

// 服务发现管理
router.get('/services', async (req, res, next) => {
  try {
    const services = await serviceDiscovery.getAllServices();
    const stats = await serviceDiscovery.getServiceStats();

    res.json({
      services,
      stats
    });
  } catch (error) {
    logger.error('Get services error:', error);
    next(createError('Failed to fetch services', 500));
  }
});

// 获取特定服务信息
router.get('/services/:serviceName', async (req, res, next) => {
  try {
    const { serviceName } = req.params;
    const service = await serviceDiscovery.getService(serviceName);

    if (!service) {
      return next(createError('Service not found', 404));
    }

    res.json({ service });
  } catch (error) {
    logger.error('Get service error:', error);
    next(createError('Failed to fetch service', 500));
  }
});

// 注册服务
router.post('/services', async (req, res, next) => {
  try {
    const { name, url, version, metadata } = req.body;

    if (!name || !url) {
      return next(createError('name and url are required', 400));
    }

    await serviceDiscovery.registerService({
      name,
      url,
      status: 'unknown',
      version,
      metadata
    });

    res.json({
      message: 'Service registered',
      service: { name, url, version, metadata }
    });
  } catch (error) {
    logger.error('Register service error:', error);
    next(createError('Failed to register service', 500));
  }
});

// 注销服务
router.delete('/services/:serviceName', async (req, res, next) => {
  try {
    const { serviceName } = req.params;
    const removed = await serviceDiscovery.unregisterService(serviceName);

    if (!removed) {
      return next(createError('Service not found', 404));
    }

    res.json({
      message: 'Service unregistered',
      service: serviceName
    });
  } catch (error) {
    logger.error('Unregister service error:', error);
    next(createError('Failed to unregister service', 500));
  }
});

// 执行服务健康检查
router.post('/services/:serviceName/health-check', async (req, res, next) => {
  try {
    const { serviceName } = req.params;
    const result = await serviceDiscovery.performHealthCheck(serviceName);

    res.json({ result });
  } catch (error) {
    logger.error('Service health check error:', error);
    next(createError('Failed to perform health check', 500));
  }
});

// 获取健康的服务
router.get('/services/healthy', async (req, res, next) => {
  try {
    const services = await serviceDiscovery.getHealthyServices();

    res.json({
      services,
      count: services.length
    });
  } catch (error) {
    logger.error('Get healthy services error:', error);
    next(createError('Failed to fetch healthy services', 500));
  }
});

// 服务统计信息
router.get('/services/stats', async (req, res, next) => {
  try {
    const stats = await serviceDiscovery.getServiceStats();
    res.json({ stats });
  } catch (error) {
    logger.error('Get service stats error:', error);
    next(createError('Failed to fetch service statistics', 500));
  }
});

export default router;
