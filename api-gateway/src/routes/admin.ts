import { Router } from 'express';
import axios from 'axios';
import { db } from '../server';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import { clearCache } from '../middleware/cache';

const router = Router();

const JAVBUS_API_URL = process.env.JAVBUS_API_URL || 'http://javbus-api:3000';
const JAVSP_API_URL = process.env.JAVSP_API_URL || 'http://javsp:8000';

// 系统健康检查
router.get('/health', async (req, res, next) => {
  try {
    const services = {
      database: false,
      javbus_api: false,
      javsp_api: false
    };
    
    // 检查数据库
    try {
      await db.query('SELECT 1');
      services.database = true;
    } catch (error) {
      logger.error('Database health check failed:', error);
    }
    
    // 检查JavBus API
    try {
      await axios.get(`${JAVBUS_API_URL}/health`, { timeout: 5000 });
      services.javbus_api = true;
    } catch (error) {
      logger.error('JavBus API health check failed:', error);
    }
    
    // 检查JavSP API
    try {
      await axios.get(`${JAVSP_API_URL}/health`, { timeout: 5000 });
      services.javsp_api = true;
    } catch (error) {
      logger.error('JavSP API health check failed:', error);
    }
    
    const allHealthy = Object.values(services).every(status => status);
    
    res.status(allHealthy ? 200 : 503).json({
      status: allHealthy ? 'healthy' : 'degraded',
      services,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Admin health check error:', error);
    next(createError('Failed to perform health check', 500));
  }
});

// 批量数据操作
router.post('/batch/scrape', async (req, res, next) => {
  try {
    const { movie_ids, sources = ['javbus'] } = req.body;
    
    if (!Array.isArray(movie_ids) || movie_ids.length === 0) {
      return next(createError('movie_ids array is required', 400));
    }
    
    if (movie_ids.length > 100) {
      return next(createError('Maximum 100 movies per batch', 400));
    }
    
    const results = [];
    
    for (const movieId of movie_ids) {
      try {
        const movieResults = [];
        
        // 从各个源抓取数据
        for (const source of sources) {
          if (source === 'javbus') {
            try {
              const response = await axios.get(`${JAVBUS_API_URL}/api/movies/${movieId}`, {
                timeout: 15000
              });
              movieResults.push({
                source: 'javbus',
                status: 'success',
                data: response.data
              });
            } catch (error) {
              movieResults.push({
                source: 'javbus',
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }
          
          if (source === 'javsp') {
            try {
              const response = await axios.post(`${JAVSP_API_URL}/scrape`, {
                movie_id: movieId
              }, {
                timeout: 30000
              });
              movieResults.push({
                source: 'javsp',
                status: 'success',
                data: response.data
              });
            } catch (error) {
              movieResults.push({
                source: 'javsp',
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }
        }
        
        results.push({
          movie_id: movieId,
          results: movieResults
        });
        
      } catch (error) {
        results.push({
          movie_id: movieId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    res.json({
      batch_id: `batch_${Date.now()}`,
      total_movies: movie_ids.length,
      results
    });
  } catch (error) {
    logger.error('Batch scrape error:', error);
    next(createError('Failed to perform batch scrape', 500));
  }
});

// 数据库维护
router.post('/maintenance/cleanup', async (req, res, next) => {
  try {
    const { action } = req.body;
    
    let result: any = {};
    
    switch (action) {
      case 'remove_duplicates':
        // 移除重复的影片记录
        const duplicateMovies = await db.query(`
          DELETE FROM movies 
          WHERE id NOT IN (
            SELECT MIN(id) 
            FROM movies 
            GROUP BY movie_id
          )
        `);
        result.removed_duplicate_movies = duplicateMovies.rowCount;
        break;
        
      case 'clean_orphaned_actors':
        // 清理没有关联影片的演员
        const orphanedActors = await db.query(`
          DELETE FROM actors 
          WHERE id NOT IN (
            SELECT DISTINCT actor_id 
            FROM movie_actors 
            WHERE actor_id IS NOT NULL
          )
        `);
        result.removed_orphaned_actors = orphanedActors.rowCount;
        break;
        
      case 'update_stats':
        // 更新统计信息
        await db.query('ANALYZE');
        result.stats_updated = true;
        break;
        
      default:
        return next(createError('Invalid maintenance action', 400));
    }
    
    res.json({
      action,
      result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Maintenance cleanup error:', error);
    next(createError('Failed to perform maintenance', 500));
  }
});

// 缓存管理
router.post('/cache/manage', async (req, res, next) => {
  try {
    const { action, pattern = '*' } = req.body;
    
    switch (action) {
      case 'clear':
        await clearCache(pattern);
        res.json({ message: 'Cache cleared', pattern });
        break;
        
      case 'warm':
        // 预热缓存 - 获取热门数据
        const popularMovies = await db.query(`
          SELECT movie_id FROM movies 
          ORDER BY created_at DESC 
          LIMIT 50
        `);
        
        // 这里可以添加预热逻辑
        res.json({ 
          message: 'Cache warming initiated', 
          items: popularMovies.rows.length 
        });
        break;
        
      default:
        return next(createError('Invalid cache action', 400));
    }
  } catch (error) {
    logger.error('Cache management error:', error);
    next(createError('Failed to manage cache', 500));
  }
});

// 系统配置
router.get('/config', async (req, res, next) => {
  try {
    const config = await db.query('SELECT * FROM system_config ORDER BY config_key');
    
    res.json({
      config: config.rows.reduce((acc: any, row: any) => {
        acc[row.config_key] = row.config_value;
        return acc;
      }, {})
    });
  } catch (error) {
    logger.error('Get config error:', error);
    next(createError('Failed to fetch configuration', 500));
  }
});

router.post('/config', async (req, res, next) => {
  try {
    const { config_key, config_value, description } = req.body;
    
    if (!config_key || config_value === undefined) {
      return next(createError('config_key and config_value are required', 400));
    }
    
    await db.query(`
      INSERT INTO system_config (config_key, config_value, description)
      VALUES ($1, $2, $3)
      ON CONFLICT (config_key)
      DO UPDATE SET 
        config_value = EXCLUDED.config_value,
        description = EXCLUDED.description,
        updated_at = CURRENT_TIMESTAMP
    `, [config_key, config_value, description]);
    
    res.json({
      message: 'Configuration updated',
      config_key,
      config_value
    });
  } catch (error) {
    logger.error('Update config error:', error);
    next(createError('Failed to update configuration', 500));
  }
});

export default router;
