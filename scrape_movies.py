#!/usr/bin/env python3
"""
JAV影片采集脚本
从JavBus API获取影片数据并保存到PostgreSQL数据库
"""

import json
import time
import sys
import os
from datetime import datetime
from urllib.parse import urlparse

# 配置
JAVBUS_API_URL = "http://localhost:8922"
DATABASE_URL = "postgresql://javuser:javpass123@localhost:5433/javdb"
STORAGE_PATH = "./storage"

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def fetch_movie_detail(movie_id):
    """获取影片详情"""
    try:
        url = f"{JAVBUS_API_URL}/api/movies/{movie_id}"
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"获取影片 {movie_id} 失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"请求影片 {movie_id} 出错: {e}")
        return None

def fetch_magnets(movie_id, gid, uc):
    """获取磁力链接"""
    try:
        url = f"{JAVBUS_API_URL}/api/magnets/{movie_id}?gid={gid}&uc={uc}"
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"获取磁力链接 {movie_id} 失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"请求磁力链接 {movie_id} 出错: {e}")
        return []

def search_movies(keyword, page=1):
    """搜索影片"""
    try:
        url = f"{JAVBUS_API_URL}/api/movies/search?keyword={keyword}&page={page}"
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"搜索失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"搜索出错: {e}")
        return None

def save_movie_to_db(conn, movie_data):
    """保存影片数据到数据库"""
    try:
        cursor = conn.cursor()
        
        # 检查影片是否已存在
        cursor.execute("SELECT id FROM movies WHERE movie_id = %s", (movie_data['id'],))
        if cursor.fetchone():
            print(f"影片 {movie_data['id']} 已存在，跳过")
            return False
        
        # 插入影片基本信息
        insert_movie_sql = """
        INSERT INTO movies (movie_id, title, original_title, release_date, duration, 
                           cover_url, gid, uc, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        """
        
        release_date = None
        if movie_data.get('date'):
            try:
                release_date = datetime.strptime(movie_data['date'], '%Y-%m-%d').date()
            except:
                pass
        
        cursor.execute(insert_movie_sql, (
            movie_data['id'],
            movie_data.get('title', ''),
            movie_data.get('title', ''),
            release_date,
            movie_data.get('videoLength'),
            movie_data.get('img', ''),
            movie_data.get('gid', ''),
            movie_data.get('uc', ''),
            datetime.now(),
            datetime.now()
        ))
        
        movie_uuid = cursor.fetchone()[0]
        
        # 保存演员信息
        if movie_data.get('stars'):
            for star in movie_data['stars']:
                # 插入或获取演员
                cursor.execute("""
                    INSERT INTO actors (actor_id, name, created_at, updated_at)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (actor_id) DO UPDATE SET updated_at = %s
                    RETURNING id
                """, (star['id'], star['name'], datetime.now(), datetime.now(), datetime.now()))
                
                actor_uuid = cursor.fetchone()[0]
                
                # 关联影片和演员
                cursor.execute("""
                    INSERT INTO movie_actors (movie_id, actor_id, role)
                    VALUES (%s, %s, %s)
                    ON CONFLICT DO NOTHING
                """, (movie_uuid, actor_uuid, 'main'))
        
        # 保存分类信息
        if movie_data.get('genres'):
            for genre in movie_data['genres']:
                # 插入或获取分类
                cursor.execute("""
                    INSERT INTO genres (genre_id, name, created_at, updated_at)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (genre_id) DO UPDATE SET updated_at = %s
                    RETURNING id
                """, (genre['id'], genre['name'], datetime.now(), datetime.now(), datetime.now()))
                
                genre_uuid = cursor.fetchone()[0]
                
                # 关联影片和分类
                cursor.execute("""
                    INSERT INTO movie_genres (movie_id, genre_id)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, (movie_uuid, genre_uuid))
        
        # 保存预览图片
        if movie_data.get('samples'):
            for i, sample in enumerate(movie_data['samples']):
                cursor.execute("""
                    INSERT INTO sample_images (movie_id, image_id, url, thumbnail_url, 
                                             alt_text, sort_order, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT DO NOTHING
                """, (movie_uuid, sample['id'], sample['src'], sample['thumbnail'],
                     sample.get('alt', ''), i, datetime.now()))
        
        conn.commit()
        print(f"✅ 影片 {movie_data['id']} 保存成功")
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 保存影片 {movie_data['id']} 失败: {e}")
        return False

def save_magnets_to_db(conn, movie_id, magnets):
    """保存磁力链接到数据库"""
    try:
        cursor = conn.cursor()
        
        # 获取影片UUID
        cursor.execute("SELECT id FROM movies WHERE movie_id = %s", (movie_id,))
        result = cursor.fetchone()
        if not result:
            print(f"影片 {movie_id} 不存在")
            return False
        
        movie_uuid = result[0]
        
        for magnet in magnets:
            # 解析大小
            size_bytes = magnet.get('numberSize', 0)
            
            # 解析日期
            share_date = None
            if magnet.get('shareDate'):
                try:
                    share_date = datetime.strptime(magnet['shareDate'], '%Y-%m-%d').date()
                except:
                    pass
            
            cursor.execute("""
                INSERT INTO magnets (movie_id, magnet_id, link, title, size_text, 
                                   size_bytes, share_date, is_hd, has_subtitle, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (magnet_id) DO NOTHING
            """, (movie_uuid, magnet['id'], magnet['link'], magnet['title'],
                 magnet.get('size', ''), size_bytes, share_date,
                 magnet.get('isHD', False), magnet.get('hasSubtitle', False),
                 datetime.now()))
        
        conn.commit()
        print(f"✅ 磁力链接保存成功: {len(magnets)} 个")
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 保存磁力链接失败: {e}")
        return False

def scrape_movie(movie_id):
    """采集单个影片的完整数据"""
    print(f"\n🎬 开始采集影片: {movie_id}")
    
    # 获取数据库连接
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        # 获取影片详情
        movie_data = fetch_movie_detail(movie_id)
        if not movie_data:
            return False
        
        # 保存影片数据
        if save_movie_to_db(conn, movie_data):
            # 获取磁力链接
            if movie_data.get('gid') and movie_data.get('uc') is not None:
                magnets = fetch_magnets(movie_id, movie_data['gid'], movie_data['uc'])
                if magnets:
                    save_magnets_to_db(conn, movie_id, magnets)
            
            return True
        
        return False
        
    finally:
        conn.close()

def scrape_search_results(keyword, max_pages=3):
    """采集搜索结果中的影片"""
    print(f"\n🔍 开始采集搜索结果: {keyword}")
    
    total_scraped = 0
    
    for page in range(1, max_pages + 1):
        print(f"\n📄 处理第 {page} 页...")
        
        search_result = search_movies(keyword, page)
        if not search_result or not search_result.get('movies'):
            print(f"第 {page} 页没有数据")
            break
        
        movies = search_result['movies']
        print(f"找到 {len(movies)} 部影片")
        
        for movie in movies:
            movie_id = movie['id']
            if scrape_movie(movie_id):
                total_scraped += 1
            
            # 避免请求过快
            time.sleep(2)
        
        # 检查是否有下一页
        if not search_result.get('pagination', {}).get('hasNextPage'):
            break
        
        # 页面间隔
        time.sleep(3)
    
    print(f"\n✅ 采集完成! 总共采集了 {total_scraped} 部影片")
    return total_scraped

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python scrape_movies.py <movie_id>           # 采集单个影片")
        print("  python scrape_movies.py search <keyword>     # 搜索并采集")
        print("  python scrape_movies.py batch <keyword> <pages>  # 批量采集")
        return
    
    command = sys.argv[1]
    
    if command == "search" and len(sys.argv) >= 3:
        keyword = sys.argv[2]
        max_pages = int(sys.argv[3]) if len(sys.argv) > 3 else 3
        scrape_search_results(keyword, max_pages)
    
    elif command == "batch" and len(sys.argv) >= 3:
        keyword = sys.argv[2]
        max_pages = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        scrape_search_results(keyword, max_pages)
    
    else:
        # 采集单个影片
        movie_id = command
        scrape_movie(movie_id)

if __name__ == "__main__":
    main()
