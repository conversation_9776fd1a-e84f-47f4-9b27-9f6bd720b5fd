// 数据库模型定义

export interface Movie {
  id: string;
  movie_id: string;
  title: string;
  original_title?: string;
  release_date?: Date;
  duration?: number;
  rating?: number;
  plot?: string;
  cover_url?: string;
  cover_local_path?: string;
  trailer_url?: string;
  gid?: string;
  uc?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Actor {
  id: string;
  actor_id: string;
  name: string;
  japanese_name?: string;
  english_name?: string;
  birthday?: Date;
  height?: number;
  bust?: number;
  waist?: number;
  hip?: number;
  birthplace?: string;
  hobby?: string;
  avatar_url?: string;
  avatar_local_path?: string;
  data_sources?: DataSources; // 多源数据信息
  created_at: Date;
  updated_at: Date;
}

// 多源数据结构
export interface DataSources {
  [sourceName: string]: SourceData;
}

export interface SourceData {
  actor_id?: string;
  name?: string;
  japanese_name?: string;
  english_name?: string;
  birthday?: string;
  height?: number;
  bust?: number;
  waist?: number;
  hip?: number;
  birthplace?: string;
  hobby?: string;
  avatar_url?: string;
  scraped_at: string;
  data_quality: number; // 数据质量评分 0-100
  confidence: number; // 匹配置信度 0-100
}

// 演员匹配结果
export interface ActorMatchResult {
  actor_id: string;
  confidence: number;
  match_type: 'exact' | 'fuzzy' | 'alias' | 'id_match';
  matched_fields: string[];
  source_data: SourceData;
}

// 头像质量评估结果
export interface AvatarQualityResult {
  url: string;
  quality_score: number;
  resolution?: string;
  file_size?: number;
  format?: string;
  issues: string[];
  source: string;
}

export interface Studio {
  id: string;
  studio_id: string;
  name: string;
  japanese_name?: string;
  created_at: Date;
}

export interface Publisher {
  id: string;
  publisher_id: string;
  name: string;
  japanese_name?: string;
  created_at: Date;
}

export interface Director {
  id: string;
  director_id: string;
  name: string;
  japanese_name?: string;
  created_at: Date;
}

export interface Series {
  id: string;
  series_id: string;
  name: string;
  japanese_name?: string;
  created_at: Date;
}

export interface Genre {
  id: string;
  genre_id: string;
  name: string;
  japanese_name?: string;
  created_at: Date;
}

export interface Magnet {
  id: string;
  movie_id: string;
  magnet_id: string;
  link: string;
  title?: string;
  size_text?: string;
  size_bytes?: number;
  share_date?: Date;
  is_hd: boolean;
  has_subtitle: boolean;
  created_at: Date;
}

export interface SampleImage {
  id: string;
  movie_id: string;
  image_id: string;
  url: string;
  thumbnail_url?: string;
  local_path?: string;
  alt_text?: string;
  sort_order: number;
  created_at: Date;
}

export interface MetadataSource {
  id: string;
  movie_id: string;
  source_name: string;
  source_url?: string;
  scraped_at: Date;
  data_quality: number;
  created_at: Date;
}

// API 响应类型
export interface MovieResponse {
  id: string;
  title: string;
  img: string;
  date: string;
  tags: string[];
  actors?: ActorResponse[];
  genres?: GenreResponse[];
  studio?: StudioResponse;
  publisher?: PublisherResponse;
  director?: DirectorResponse;
  series?: SeriesResponse;
  magnets?: MagnetResponse[];
  samples?: SampleImageResponse[];
}

export interface ActorResponse {
  id: string;
  name: string;
  avatar?: string;
}

export interface GenreResponse {
  id: string;
  name: string;
}

export interface StudioResponse {
  id: string;
  name: string;
}

export interface PublisherResponse {
  id: string;
  name: string;
}

export interface DirectorResponse {
  id: string;
  name: string;
}

export interface SeriesResponse {
  id: string;
  name: string;
}

export interface MagnetResponse {
  id: string;
  link: string;
  title: string;
  size: string;
  shareDate: string;
  isHD: boolean;
  hasSubtitle: boolean;
}

export interface SampleImageResponse {
  id: string;
  src: string;
  thumbnail: string;
  alt: string;
}
