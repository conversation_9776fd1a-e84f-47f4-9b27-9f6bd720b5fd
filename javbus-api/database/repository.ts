import { query } from './connection.js';
import type { Movie, Actor, Studio, Publisher, Director, Series, Genre, Magnet, SampleImage, MetadataSource } from './models.js';

// 影片数据操作
export class MovieRepository {
  // 保存或更新影片信息
  static async saveMovie(movieData: any): Promise<string> {
    const {
      movie_id, title, original_title, release_date, duration, rating, plot,
      cover_url, cover_local_path, trailer_url, gid, uc
    } = movieData;

    const result = await query(`
      INSERT INTO movies (movie_id, title, original_title, release_date, duration, rating, plot, cover_url, cover_local_path, trailer_url, gid, uc)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      ON CONFLICT (movie_id) 
      DO UPDATE SET 
        title = EXCLUDED.title,
        original_title = EXCLUDED.original_title,
        release_date = EXCLUDED.release_date,
        duration = EXCLUDED.duration,
        rating = EXCLUDED.rating,
        plot = EXCLUDED.plot,
        cover_url = EXCLUDED.cover_url,
        cover_local_path = EXCLUDED.cover_local_path,
        trailer_url = EXCLUDED.trailer_url,
        gid = EXCLUDED.gid,
        uc = EXCLUDED.uc,
        updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `, [movie_id, title, original_title, release_date, duration, rating, plot, cover_url, cover_local_path, trailer_url, gid, uc]);

    return result.rows[0].id;
  }

  // 根据番号获取影片
  static async getMovieByMovieId(movieId: string): Promise<Movie | null> {
    const result = await query('SELECT * FROM movies WHERE movie_id = $1', [movieId]);
    return result.rows[0] || null;
  }

  // 获取影片列表
  static async getMovies(page: number = 1, limit: number = 20): Promise<Movie[]> {
    const offset = (page - 1) * limit;
    const result = await query(`
      SELECT * FROM movies 
      ORDER BY release_date DESC, created_at DESC 
      LIMIT $1 OFFSET $2
    `, [limit, offset]);
    return result.rows;
  }

  // 搜索影片
  static async searchMovies(keyword: string, page: number = 1, limit: number = 20): Promise<Movie[]> {
    const offset = (page - 1) * limit;
    const result = await query(`
      SELECT * FROM movies 
      WHERE title ILIKE $1 OR movie_id ILIKE $1 OR original_title ILIKE $1
      ORDER BY release_date DESC, created_at DESC 
      LIMIT $2 OFFSET $3
    `, [`%${keyword}%`, limit, offset]);
    return result.rows;
  }
}

// 演员数据操作
export class ActorRepository {
  // 保存或更新演员信息（支持多源数据）
  static async saveActor(actorData: any): Promise<string> {
    const {
      actor_id, name, japanese_name, english_name, birthday, height, bust, waist, hip,
      birthplace, hobby, avatar_url, avatar_local_path, data_sources
    } = actorData;

    const result = await query(`
      INSERT INTO actors (actor_id, name, japanese_name, english_name, birthday, height, bust, waist, hip, birthplace, hobby, avatar_url, avatar_local_path, data_sources)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      ON CONFLICT (actor_id)
      DO UPDATE SET
        name = EXCLUDED.name,
        japanese_name = EXCLUDED.japanese_name,
        english_name = EXCLUDED.english_name,
        birthday = EXCLUDED.birthday,
        height = EXCLUDED.height,
        bust = EXCLUDED.bust,
        waist = EXCLUDED.waist,
        hip = EXCLUDED.hip,
        birthplace = EXCLUDED.birthplace,
        hobby = EXCLUDED.hobby,
        avatar_url = EXCLUDED.avatar_url,
        avatar_local_path = EXCLUDED.avatar_local_path,
        data_sources = EXCLUDED.data_sources,
        updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `, [actor_id, name, japanese_name, english_name, birthday, height, bust, waist, hip, birthplace, hobby, avatar_url, avatar_local_path, JSON.stringify(data_sources || {})]);

    return result.rows[0].id;
  }

  // 根据演员ID获取演员信息
  static async getActorByActorId(actorId: string): Promise<Actor | null> {
    const result = await query('SELECT * FROM actors WHERE actor_id = $1', [actorId]);
    if (result.rows[0]) {
      const actor = result.rows[0];
      // 解析JSON字段
      if (actor.data_sources) {
        actor.data_sources = JSON.parse(actor.data_sources);
      }
      return actor;
    }
    return null;
  }

  // 根据姓名搜索演员（支持模糊匹配）
  static async searchActorsByName(name: string, limit: number = 10): Promise<Actor[]> {
    const result = await query(`
      SELECT * FROM actors
      WHERE name ILIKE $1 OR japanese_name ILIKE $1 OR english_name ILIKE $1
      ORDER BY
        CASE
          WHEN name = $2 THEN 1
          WHEN japanese_name = $2 THEN 2
          WHEN english_name = $2 THEN 3
          ELSE 4
        END,
        name
      LIMIT $3
    `, [`%${name}%`, name, limit]);

    return result.rows.map(actor => {
      if (actor.data_sources) {
        actor.data_sources = JSON.parse(actor.data_sources);
      }
      return actor;
    });
  }

  // 合并多源数据到现有演员
  static async mergeMultiSourceData(actorId: string, sourceName: string, sourceData: any): Promise<void> {
    const actor = await this.getActorByActorId(actorId);
    if (!actor) {
      throw new Error(`Actor not found: ${actorId}`);
    }

    const dataSources = actor.data_sources || {};
    dataSources[sourceName] = {
      ...sourceData,
      scraped_at: new Date().toISOString(),
      data_quality: this.calculateDataQuality(sourceData),
      confidence: 100 // 直接指定的数据置信度为100
    };

    await query(`
      UPDATE actors
      SET data_sources = $1, updated_at = CURRENT_TIMESTAMP
      WHERE actor_id = $2
    `, [JSON.stringify(dataSources), actorId]);
  }

  // 计算数据质量评分
  static calculateDataQuality(data: any): number {
    let score = 0;
    const fields = ['name', 'japanese_name', 'birthday', 'height', 'avatar_url'];

    fields.forEach(field => {
      if (data[field] && data[field] !== '') {
        score += 20;
      }
    });

    // 额外加分项
    if (data.bust && data.waist && data.hip) score += 10;
    if (data.birthplace) score += 5;
    if (data.hobby) score += 5;

    return Math.min(score, 100);
  }

  // 获取需要补全数据的演员
  static async getActorsNeedingCompletion(limit: number = 50): Promise<Actor[]> {
    const result = await query(`
      SELECT * FROM actors
      WHERE
        avatar_local_path IS NULL OR
        birthday IS NULL OR
        height IS NULL OR
        japanese_name IS NULL
      ORDER BY updated_at ASC
      LIMIT $1
    `, [limit]);

    return result.rows.map(actor => {
      if (actor.data_sources) {
        actor.data_sources = JSON.parse(actor.data_sources);
      }
      return actor;
    });
  }

  // 批量更新演员数据
  static async batchUpdateActors(actors: any[]): Promise<number> {
    let updatedCount = 0;

    for (const actorData of actors) {
      try {
        await this.saveActor(actorData);
        updatedCount++;
      } catch (error) {
        console.error(`Failed to update actor ${actorData.actor_id}:`, error);
      }
    }

    return updatedCount;
  }
}

// 磁力链接数据操作
export class MagnetRepository {
  // 保存磁力链接
  static async saveMagnets(movieId: string, magnets: any[]): Promise<void> {
    for (const magnet of magnets) {
      await query(`
        INSERT INTO magnets (movie_id, magnet_id, link, title, size_text, size_bytes, share_date, is_hd, has_subtitle)
        VALUES (
          (SELECT id FROM movies WHERE movie_id = $1),
          $2, $3, $4, $5, $6, $7, $8, $9
        )
        ON CONFLICT (magnet_id) DO NOTHING
      `, [movieId, magnet.id, magnet.link, magnet.title, magnet.size, magnet.numberSize, magnet.shareDate, magnet.isHD, magnet.hasSubtitle]);
    }
  }

  // 获取影片的磁力链接
  static async getMagnetsByMovieId(movieId: string): Promise<Magnet[]> {
    const result = await query(`
      SELECT m.* FROM magnets m
      JOIN movies mv ON m.movie_id = mv.id
      WHERE mv.movie_id = $1
      ORDER BY m.size_bytes DESC, m.share_date DESC
    `, [movieId]);
    return result.rows;
  }
}

// 预览图片数据操作
export class SampleImageRepository {
  // 保存预览图片
  static async saveSampleImages(movieId: string, samples: any[]): Promise<void> {
    for (let i = 0; i < samples.length; i++) {
      const sample = samples[i];
      await query(`
        INSERT INTO sample_images (movie_id, image_id, url, thumbnail_url, local_path, alt_text, sort_order)
        VALUES (
          (SELECT id FROM movies WHERE movie_id = $1),
          $2, $3, $4, $5, $6, $7
        )
        ON CONFLICT (movie_id, image_id) DO NOTHING
      `, [movieId, sample.id, sample.src, sample.thumbnail, null, sample.alt, i]);
    }
  }
}

// 元数据来源追踪
export class MetadataSourceRepository {
  // 记录数据来源
  static async recordSource(movieId: string, sourceName: string, sourceUrl?: string, dataQuality: number = 50): Promise<void> {
    await query(`
      INSERT INTO metadata_sources (movie_id, source_name, source_url, data_quality)
      VALUES (
        (SELECT id FROM movies WHERE movie_id = $1),
        $2, $3, $4
      )
      ON CONFLICT (movie_id, source_name) 
      DO UPDATE SET 
        source_url = EXCLUDED.source_url,
        data_quality = EXCLUDED.data_quality,
        scraped_at = CURRENT_TIMESTAMP
    `, [movieId, sourceName, sourceUrl, dataQuality]);
  }
}
