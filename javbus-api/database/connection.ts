import { Pool, type PoolClient } from 'pg';

// 数据库连接池
let pool: Pool | null = null;

// 初始化数据库连接
export function initDatabase(): Pool {
  if (!pool) {
    const databaseUrl = process.env.DATABASE_URL || 'postgresql://javuser:javpass123@localhost:5432/javdb';
    
    pool = new Pool({
      connectionString: databaseUrl,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
      process.exit(-1);
    });

    console.log('Database connection pool initialized');
  }
  
  return pool;
}

// 获取数据库连接
export async function getConnection(): Promise<PoolClient> {
  const dbPool = initDatabase();
  return await dbPool.connect();
}

// 执行查询
export async function query(text: string, params?: any[]): Promise<any> {
  const client = await getConnection();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

// 关闭数据库连接
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('Database connection pool closed');
  }
}
