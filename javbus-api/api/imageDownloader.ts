// 图片下载服务
import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';
import got from 'got';

// 存储路径配置
const STORAGE_BASE = process.env.STORAGE_PATH || '/app/storage';

// 支持的图片格式
const SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

// 下载图片
export async function downloadImage(
  imageUrl: string,
  category: 'covers' | 'actors' | 'samples',
  filename: string,
): Promise<string | null> {
  try {
    // 确保目录存在
    const categoryPath = path.join(STORAGE_BASE, category);
    if (!fs.existsSync(categoryPath)) {
      fs.mkdirSync(categoryPath, { recursive: true });
    }

    // 获取图片扩展名
    const urlPath = new URL(imageUrl).pathname;
    const ext = path.extname(urlPath).toLowerCase();
    const finalExt = SUPPORTED_FORMATS.includes(ext) ? ext : '.jpg';

    // 生成文件路径
    const fileName = `${filename}${finalExt}`;
    const filePath = path.join(categoryPath, fileName);

    // 如果文件已存在，返回路径
    if (fs.existsSync(filePath)) {
      return path.relative(STORAGE_BASE, filePath);
    }

    // 下载图片
    const downloadStream = got.stream(imageUrl, {
      timeout: {
        request: 30000,
      },
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    const writeStream = fs.createWriteStream(filePath);

    await pipeline(downloadStream, writeStream);

    // 验证文件大小
    const stats = fs.statSync(filePath);
    if (stats.size < 1024) {
      // 小于1KB可能是错误页面
      fs.unlinkSync(filePath);
      throw new Error('Downloaded file too small, likely an error page');
    }

    console.log(`Downloaded image: ${fileName} (${formatBytes(stats.size)})`);
    return path.relative(STORAGE_BASE, filePath);
  } catch (error) {
    console.error(`Failed to download image ${imageUrl}:`, (error as Error).message);
    return null;
  }
}

// 批量下载图片
export async function downloadImages(
  imageUrls: string[],
  category: 'covers' | 'actors' | 'samples',
  baseFilename: string,
): Promise<string[]> {
  const results: string[] = [];

  for (let i = 0; i < imageUrls.length; i++) {
    const url = imageUrls[i];
    const filename = `${baseFilename}_${i + 1}`;

    if (!url) {
      console.warn(`Skipping empty URL at index ${i}`);
      continue;
    }

    try {
      const localPath = await downloadImage(url, category, filename);
      if (localPath !== null) {
        results.push(localPath);
      }
    } catch (error) {
      console.error(`Failed to download image ${i + 1}:`, error);
    }
  }

  return results;
}

// 清理过期图片
export async function cleanupOldImages(
  category: 'covers' | 'actors' | 'samples',
  daysOld: number = 30,
): Promise<void> {
  try {
    const categoryPath = path.join(STORAGE_BASE, category);
    if (!fs.existsSync(categoryPath)) {
      return;
    }

    const files = fs.readdirSync(categoryPath);
    const cutoffTime = Date.now() - daysOld * 24 * 60 * 60 * 1000;
    let deletedCount = 0;

    for (const file of files) {
      const filePath = path.join(categoryPath, file);
      const stats = fs.statSync(filePath);

      if (stats.mtime.getTime() < cutoffTime) {
        fs.unlinkSync(filePath);
        deletedCount++;
      }
    }

    console.log(`Cleaned up ${deletedCount} old images from ${category}`);
  } catch (error) {
    console.error(`Failed to cleanup old images in ${category}:`, error);
  }
}

// 获取存储统计信息
export function getStorageStats(): { [key: string]: any } {
  const stats: { [key: string]: { count: number; size: number; sizeFormatted?: string } } = {
    covers: { count: 0, size: 0 },
    actors: { count: 0, size: 0 },
    samples: { count: 0, size: 0 },
    total: { count: 0, size: 0 },
  };

  for (const category of ['covers', 'actors', 'samples']) {
    const categoryPath = path.join(STORAGE_BASE, category);

    if (fs.existsSync(categoryPath)) {
      const files = fs.readdirSync(categoryPath);

      for (const file of files) {
        const filePath = path.join(categoryPath, file);
        const fileStats = fs.statSync(filePath);

        if (fileStats.isFile()) {
          stats[category]!.count++;
          stats[category]!.size += fileStats.size;
        }
      }
    }
  }

  // 计算总计
  stats.total!.count = stats.covers!.count + stats.actors!.count + stats.samples!.count;
  stats.total!.size = stats.covers!.size + stats.actors!.size + stats.samples!.size;

  // 格式化大小
  for (const category of ['covers', 'actors', 'samples', 'total']) {
    stats[category]!.sizeFormatted = formatBytes(stats[category]!.size);
  }

  return stats;
}

// 格式化字节大小
function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// 验证图片URL
export function isValidImageUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    const pathname = parsedUrl.pathname.toLowerCase();

    return (
      SUPPORTED_FORMATS.some((format) => pathname.endsWith(format)) ||
      pathname.includes('/pics/') ||
      pathname.includes('/images/') ||
      pathname.includes('/img/')
    );
  } catch {
    return false;
  }
}
