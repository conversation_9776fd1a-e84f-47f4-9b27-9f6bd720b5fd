// 数据保存服务
import {
  MovieRepository,
  ActorRepository,
  MagnetRepository,
  SampleImageRepository,
  MetadataSourceRepository,
} from '../database/repository.js';
import { downloadImage } from './imageDownloader.js';

export class DataService {
  // 保存完整的影片数据
  static async saveMovieData(
    movieData: Record<string, unknown>,
    source: string = 'javbus',
  ): Promise<void> {
    try {
      // 1. 保存影片基础信息
      const movieId = await MovieRepository.saveMovie({
        movie_id: movieData.id,
        title: movieData.title,
        original_title: movieData.originalTitle,
        release_date: movieData.date ? new Date(movieData.date as string) : null,
        duration: movieData.videoLength,
        rating: movieData.rating,
        plot: movieData.plot,
        cover_url: movieData.img,
        trailer_url: movieData.trailer,
        gid: movieData.gid,
        uc: movieData.uc,
      });

      // 2. 下载并保存封面图片
      if (movieData.img) {
        try {
          const localPath = await downloadImage(movieData.img as string, 'covers', `${movieData.id}_cover`);
          if (localPath) {
            await MovieRepository.saveMovie({
              movie_id: movieData.id,
              cover_local_path: localPath,
            });
          }
        } catch (error) {
          console.error(`Failed to download cover for ${movieData.id}:`, error);
        }
      }

      // 3. 保存演员信息
      if (movieData.stars && Array.isArray(movieData.stars)) {
        for (const star of movieData.stars) {
          await ActorRepository.saveActor({
            actor_id: star.id,
            name: star.name,
            japanese_name: star.name,
          });

          // 下载演员头像
          if (star.avatar) {
            try {
              const localPath = await downloadImage(star.avatar, 'actors', `${star.id}_avatar`);
              if (localPath) {
                await ActorRepository.saveActor({
                  actor_id: star.id,
                  avatar_local_path: localPath,
                });
              }
            } catch (error) {
              console.error(`Failed to download avatar for ${star.id}:`, error);
            }
          }
        }
      }

      // 4. 保存磁力链接
      if (movieData.magnets && Array.isArray(movieData.magnets)) {
        await MagnetRepository.saveMagnets(movieData.id as string, movieData.magnets);
      }

      // 5. 保存预览图片（排除样品图）
      if (movieData.samples && Array.isArray(movieData.samples)) {
        // 过滤掉样品图，只保存封面相关的预览图
        const filteredSamples = movieData.samples.filter(
          (sample: any) =>
            !sample.alt?.toLowerCase().includes('sample') &&
            !sample.src?.toLowerCase().includes('sample'),
        );

        if (filteredSamples.length > 0) {
          await SampleImageRepository.saveSampleImages(movieData.id as string, filteredSamples);
        }
      }

      // 6. 记录数据来源
      await MetadataSourceRepository.recordSource(
        movieData.id as string,
        source,
        movieData.sourceUrl as string | undefined,
        this.calculateDataQuality(movieData),
      );

      console.log(`Successfully saved movie data: ${movieData.id}`);
    } catch (error) {
      console.error(`Failed to save movie data for ${movieData.id}:`, error);
      throw error;
    }
  }

  // 保存演员详细信息
  static async saveActorData(actorData: any, source: string = 'javbus'): Promise<void> {
    try {
      await ActorRepository.saveActor({
        actor_id: actorData.id,
        name: actorData.name,
        japanese_name: actorData.name,
        birthday: actorData.birthday ? new Date(actorData.birthday) : null,
        height: actorData.height ? parseInt(actorData.height) : null,
        bust: actorData.bust ? parseInt(actorData.bust) : null,
        waist: actorData.waistline ? parseInt(actorData.waistline) : null,
        hip: actorData.hipline ? parseInt(actorData.hipline) : null,
        birthplace: actorData.birthplace,
        hobby: actorData.hobby,
        avatar_url: actorData.avatar,
      });

      // 下载演员头像
      if (actorData.avatar) {
        try {
          const localPath = await downloadImage(
            actorData.avatar,
            'actors',
            `${actorData.id}_avatar`,
          );
          if (localPath) {
            await ActorRepository.saveActor({
              actor_id: actorData.id,
              avatar_local_path: localPath,
            });
          }
        } catch (error) {
          console.error(`Failed to download avatar for ${actorData.id}:`, error);
        }
      }

      console.log(`Successfully saved actor data: ${actorData.id}`);
    } catch (error) {
      console.error(`Failed to save actor data for ${actorData.id}:`, error);
      throw error;
    }
  }

  // 计算数据质量评分
  private static calculateDataQuality(movieData: any): number {
    let score = 0;

    // 基础信息
    if (movieData.title) score += 20;
    if (movieData.date) score += 15;
    if (movieData.img) score += 15;
    if (movieData.plot) score += 10;

    // 演员信息
    if (movieData.stars && movieData.stars.length > 0) score += 15;

    // 分类信息
    if (movieData.genres && movieData.genres.length > 0) score += 10;

    // 磁力链接
    if (movieData.magnets && movieData.magnets.length > 0) score += 10;

    // 预览图片
    if (movieData.samples && movieData.samples.length > 0) score += 5;

    return Math.min(score, 100);
  }

  // 批量保存数据
  static async batchSaveMovies(moviesData: any[], source: string = 'javbus'): Promise<void> {
    const results = [];

    for (const movieData of moviesData) {
      try {
        await this.saveMovieData(movieData, source);
        results.push({ id: movieData.id, status: 'success' });
      } catch (error) {
        results.push({ id: movieData.id, status: 'error', error: (error as Error).message });
      }
    }

    console.log(
      `Batch save completed. Success: ${results.filter((r) => r.status === 'success').length}, Failed: ${results.filter((r) => r.status === 'error').length}`,
    );
  }
}
