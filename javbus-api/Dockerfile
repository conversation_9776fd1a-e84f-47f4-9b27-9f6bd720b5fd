FROM node:lts-slim AS base

WORKDIR /app

COPY package*.json ./

# -------------------

FROM base AS prod-deps

COPY . .
RUN npm install --only=production

# -------------------

FROM base AS build

COPY . .
RUN npm install && npm run build

# -------------------

FROM node:lts-slim

RUN apt-get update && \
  apt-get install -y --no-install-recommends tini postgresql-client && \
  rm -rf /var/lib/apt/lists/*

ENV NODE_ENV=production
USER node

WORKDIR /app

COPY --chown=node:node --from=prod-deps /app/node_modules /app/node_modules
COPY --chown=node:node --from=build /app/dist /app/dist
COPY --chown=node:node --from=build /app/package.json /app/package.json

# 创建存储目录
USER root
RUN mkdir -p /app/storage/covers /app/storage/actors /app/storage/metadata
RUN chown -R node:node /app/storage
USER node

EXPOSE 3000

CMD [ "/usr/bin/tini", "--", "node", "dist/server.js" ]
