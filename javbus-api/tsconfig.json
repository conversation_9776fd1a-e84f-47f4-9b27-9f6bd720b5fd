{"compilerOptions": {"declaration": false, "removeComments": true, "sourceMap": false, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "verbatimModuleSyntax": false, "esModuleInterop": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "noUnusedLocals": false, "noUnusedParameters": false, "checkJs": false, "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./dist", "target": "ES2022"}}