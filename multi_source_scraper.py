#!/usr/bin/env python3
"""
多源协同JAV数据采集脚本
整合JavBus API、JavSP、<PERSON><PERSON><PERSON><PERSON>三个数据源
实现图片下载、数据补全、增量更新
"""

import subprocess
import json
import time
import os
import urllib.request
from urllib.parse import urlparse

class MultiSourceScraper:
    def __init__(self):
        self.javbus_url = "http://localhost:8922"
        self.javsp_url = "http://localhost:8000"
        self.javinizer_url = "http://localhost:8600"
        self.data_service_url = "http://localhost:3000"
        self.storage_path = "./storage"
        
    def run_curl_command(self, url):
        """执行curl命令获取数据"""
        try:
            result = subprocess.run(['curl', '-s', url], capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                return json.loads(result.stdout)
            return None
        except Exception as e:
            print(f"请求失败 {url}: {e}")
            return None

    def download_image(self, url, category, filename):
        """下载图片到本地存储"""
        try:
            if not url:
                return None

            # 创建目录
            category_dir = os.path.join(self.storage_path, category)
            os.makedirs(category_dir, exist_ok=True)

            # 获取文件扩展名
            parsed_url = urlparse(url)
            ext = os.path.splitext(parsed_url.path)[1] or '.jpg'
            local_path = os.path.join(category_dir, f"{filename}{ext}")

            # 创建请求，添加User-Agent避免403错误
            req = urllib.request.Request(
                url,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://www.javbus.com/'
                }
            )

            # 下载图片
            with urllib.request.urlopen(req) as response:
                with open(local_path, 'wb') as f:
                    f.write(response.read())

            print(f"📸 图片已保存: {local_path}")
            return local_path

        except Exception as e:
            print(f"❌ 下载图片失败 {url}: {e}")
            return None

    def get_javbus_data(self, movie_id):
        """从JavBus API获取数据"""
        print(f"🔍 JavBus API: 获取 {movie_id}")
        url = f"{self.javbus_url}/api/movies/{movie_id}"
        return self.run_curl_command(url)

    def get_javsp_data(self, movie_id):
        """从JavSP获取数据"""
        print(f"🔍 JavSP: 获取 {movie_id}")
        # JavSP的API端点可能不同，这里是示例
        url = f"{self.javsp_url}/api/movie/{movie_id}"
        return self.run_curl_command(url)

    def get_javinizer_data(self, movie_id):
        """从Javinizer获取数据"""
        print(f"🔍 Javinizer: 获取 {movie_id}")
        # Javinizer的API端点，这里是示例
        url = f"{self.javinizer_url}/api/movie/{movie_id}"
        return self.run_curl_command(url)

    def merge_movie_data(self, javbus_data, javsp_data=None, javinizer_data=None):
        """合并多源数据，优先级：JavSP > Javinizer > JavBus"""
        merged_data = javbus_data.copy() if javbus_data else {}
        
        # 从JavSP补充数据
        if javsp_data:
            # 补充演员头像
            if 'stars' in javsp_data:
                for i, star in enumerate(merged_data.get('stars', [])):
                    javsp_star = next((s for s in javsp_data['stars'] if s.get('name') == star.get('name')), None)
                    if javsp_star and javsp_star.get('avatar') and not star.get('avatar'):
                        merged_data['stars'][i]['avatar'] = javsp_star['avatar']
            
            # 补充其他高质量数据
            for key in ['plot', 'rating', 'trailer']:
                if javsp_data.get(key) and not merged_data.get(key):
                    merged_data[key] = javsp_data[key]
        
        # 从Javinizer补充数据
        if javinizer_data:
            # 补充元数据
            for key in ['original_title', 'english_title', 'studio_info']:
                if javinizer_data.get(key) and not merged_data.get(key):
                    merged_data[key] = javinizer_data[key]
        
        return merged_data

    def download_movie_images(self, movie_data):
        """下载影片相关图片"""
        downloaded_images = {}
        
        # 下载封面图片
        if movie_data.get('img'):
            cover_path = self.download_image(
                movie_data['img'], 
                'covers', 
                f"{movie_data['id']}_cover"
            )
            if cover_path:
                downloaded_images['cover'] = cover_path
        
        # 下载演员头像
        if movie_data.get('stars'):
            for star in movie_data['stars']:
                if star.get('avatar') or star.get('thumb'):
                    avatar_url = star.get('avatar') or star.get('thumb')
                    avatar_path = self.download_image(
                        avatar_url,
                        'actors',
                        f"{star.get('id', star['name'].replace(' ', '_'))}_avatar"
                    )
                    if avatar_path:
                        star['local_avatar'] = avatar_path
        
        # 下载预览图片
        if movie_data.get('samples'):
            for i, sample in enumerate(movie_data['samples']):
                if sample.get('src'):
                    sample_path = self.download_image(
                        sample['src'],
                        'samples',
                        f"{movie_data['id']}_sample_{i+1}"
                    )
                    if sample_path:
                        sample['local_path'] = sample_path
        
        return downloaded_images

    def save_to_database(self, movie_data):
        """保存数据到数据库"""
        try:
            # 构建SQL插入语句
            sql = f"""
            INSERT INTO movies (movie_id, title, original_title, release_date, duration, 
                              cover_url, cover_local_path, plot, rating, created_at, updated_at)
            VALUES ('{movie_data['id']}', 
                    '{movie_data.get('title', '').replace("'", "''")}',
                    '{movie_data.get('original_title', movie_data.get('title', '')).replace("'", "''")}',
                    '{movie_data.get('date', '2024-01-01')}', 
                    {movie_data.get('videoLength', 0)}, 
                    '{movie_data.get('img', '')}',
                    '{movie_data.get('cover_local_path', '')}',
                    '{movie_data.get('plot', '').replace("'", "''")}',
                    {movie_data.get('rating', 0)},
                    NOW(), NOW())
            ON CONFLICT (movie_id) DO UPDATE SET 
                title = EXCLUDED.title,
                original_title = EXCLUDED.original_title,
                cover_local_path = EXCLUDED.cover_local_path,
                plot = EXCLUDED.plot,
                rating = EXCLUDED.rating,
                updated_at = NOW();
            """
            
            # 执行SQL
            cmd = ['docker', 'exec', 'jav_postgres', 'psql', '-U', 'javuser', '-d', 'javdb', '-c', sql]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ 数据库保存成功: {movie_data['id']}")
                return True
            else:
                print(f"❌ 数据库保存失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 数据库操作出错: {e}")
            return False

    def scrape_movie_complete(self, movie_id):
        """完整采集单个影片（多源+图片）"""
        print(f"\n🎬 开始完整采集影片: {movie_id}")
        
        # 1. 从多个数据源获取数据
        javbus_data = self.get_javbus_data(movie_id)
        javsp_data = self.get_javsp_data(movie_id)
        javinizer_data = self.get_javinizer_data(movie_id)
        
        if not javbus_data:
            print(f"❌ 无法获取 {movie_id} 的基础数据")
            return False
        
        # 2. 合并多源数据
        merged_data = self.merge_movie_data(javbus_data, javsp_data, javinizer_data)
        
        # 3. 下载图片
        downloaded_images = self.download_movie_images(merged_data)
        
        # 4. 更新本地路径信息
        if downloaded_images.get('cover'):
            merged_data['cover_local_path'] = downloaded_images['cover']
        
        # 5. 保存到数据库
        success = self.save_to_database(merged_data)
        
        if success:
            print(f"✅ 完整采集成功: {movie_id}")
            print(f"   - 数据源: JavBus{'✓' if javbus_data else '✗'} JavSP{'✓' if javsp_data else '✗'} Javinizer{'✓' if javinizer_data else '✗'}")
            print(f"   - 图片: 封面{'✓' if downloaded_images.get('cover') else '✗'} 演员头像{len([s for s in merged_data.get('stars', []) if s.get('local_avatar')])} 预览图{len([s for s in merged_data.get('samples', []) if s.get('local_path')])}")
        
        return success

def main():
    """主函数"""
    import sys
    
    scraper = MultiSourceScraper()
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python3 multi_source_scraper.py <movie_id>     # 完整采集单个影片")
        print("  python3 multi_source_scraper.py test           # 测试采集")
        return
    
    command = sys.argv[1]
    
    if command == "test":
        # 测试采集
        scraper.scrape_movie_complete("SSIS-001")
    else:
        # 采集指定影片
        movie_id = command
        scraper.scrape_movie_complete(movie_id)

if __name__ == "__main__":
    main()
