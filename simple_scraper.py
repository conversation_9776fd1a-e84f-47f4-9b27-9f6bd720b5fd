#!/usr/bin/env python3
"""
简化的JAV数据采集脚本
直接使用Docker容器中的环境
"""

import subprocess
import json
import time

def run_curl_command(url):
    """执行curl命令获取数据"""
    try:
        result = subprocess.run(['curl', '-s', url], capture_output=True, text=True)
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            print(f"请求失败: {result.stderr}")
            return None
    except Exception as e:
        print(f"执行curl命令出错: {e}")
        return None

def insert_movie_to_db(movie_data):
    """直接使用psql命令插入数据到数据库"""
    try:
        # 构建SQL插入语句
        sql = f"""
        INSERT INTO movies (movie_id, title, original_title, release_date, duration, cover_url, created_at, updated_at)
        VALUES ('{movie_data['id']}', '{movie_data['title'].replace("'", "''")}', '{movie_data['title'].replace("'", "''")}', 
                '{movie_data.get('date', '2024-01-01')}', {movie_data.get('videoLength', 0)}, 
                '{movie_data.get('img', '')}', NOW(), NOW())
        ON CONFLICT (movie_id) DO UPDATE SET 
            title = EXCLUDED.title,
            updated_at = NOW();
        """
        
        # 使用docker exec执行SQL
        cmd = ['docker', 'exec', 'jav_postgres', 'psql', '-U', 'javuser', '-d', 'javdb', '-c', sql]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 影片 {movie_data['id']} 保存成功")
            return True
        else:
            print(f"❌ 保存失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 插入数据出错: {e}")
        return False

def scrape_and_save_movie(movie_id):
    """采集并保存单个影片"""
    print(f"\n🎬 开始采集影片: {movie_id}")
    
    # 获取影片详情
    url = f"http://localhost:8922/api/movies/{movie_id}"
    movie_data = run_curl_command(url)
    
    if not movie_data:
        print(f"❌ 获取影片 {movie_id} 数据失败")
        return False
    
    # 保存到数据库
    return insert_movie_to_db(movie_data)

def scrape_search_results(keyword, max_pages=3):
    """采集搜索结果"""
    print(f"\n🔍 开始采集搜索结果: {keyword}")
    
    total_scraped = 0
    
    for page in range(1, max_pages + 1):
        print(f"\n📄 处理第 {page} 页...")
        
        # 搜索影片
        search_url = f"http://localhost:8922/api/movies/search?keyword={keyword}&page={page}"
        search_result = run_curl_command(search_url)
        
        if not search_result or not search_result.get('movies'):
            print(f"第 {page} 页没有数据")
            break
        
        movies = search_result['movies']
        print(f"找到 {len(movies)} 部影片")
        
        for movie in movies:
            movie_id = movie['id']
            if scrape_and_save_movie(movie_id):
                total_scraped += 1
            
            # 避免请求过快
            time.sleep(2)
        
        # 检查是否有下一页
        if not search_result.get('pagination', {}).get('hasNextPage'):
            break
        
        # 页面间隔
        time.sleep(3)
    
    print(f"\n✅ 采集完成! 总共采集了 {total_scraped} 部影片")
    return total_scraped

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python3 simple_scraper.py <movie_id>           # 采集单个影片")
        print("  python3 simple_scraper.py search <keyword>     # 搜索并采集")
        print("  python3 simple_scraper.py batch <keyword> <pages>  # 批量采集")
        return
    
    command = sys.argv[1]
    
    if command == "search" and len(sys.argv) >= 3:
        keyword = sys.argv[2]
        max_pages = int(sys.argv[3]) if len(sys.argv) > 3 else 3
        scrape_search_results(keyword, max_pages)
    
    elif command == "batch" and len(sys.argv) >= 3:
        keyword = sys.argv[2]
        max_pages = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        scrape_search_results(keyword, max_pages)
    
    else:
        # 采集单个影片
        movie_id = command
        scrape_and_save_movie(movie_id)

if __name__ == "__main__":
    main()
