-- JAV 数据库初始化脚本
-- 创建数据库表结构

-- 设置编码
SET client_encoding = 'UTF8';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 影片信息表
CREATE TABLE movies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    movie_id VARCHAR(50) UNIQUE NOT NULL, -- 番号，如 SSIS-406
    title TEXT NOT NULL,
    original_title TEXT,
    release_date DATE,
    duration INTEGER, -- 时长（分钟）
    rating DECIMAL(3,2),
    plot TEXT, -- 剧情简介
    cover_url TEXT,
    cover_local_path TEXT,
    trailer_url TEXT,
    gid VARCHAR(50), -- JavBus gid
    uc VARCHAR(10), -- JavBus uc
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 演员信息表
CREATE TABLE actors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    actor_id VARCHAR(50) UNIQUE NOT NULL, -- 演员ID
    name VARCHAR(255) NOT NULL,
    japanese_name VARCHAR(255),
    english_name VARCHAR(255),
    birthday DATE,
    height INTEGER, -- 身高（cm）
    bust INTEGER, -- 胸围（cm）
    waist INTEGER, -- 腰围（cm）
    hip INTEGER, -- 臀围（cm）
    birthplace VARCHAR(255),
    hobby TEXT,
    avatar_url TEXT,
    avatar_local_path TEXT,
    data_sources JSONB DEFAULT '{}', -- 多源数据信息记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 制作商表
CREATE TABLE studios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    studio_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    japanese_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 发行商表
CREATE TABLE publishers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    publisher_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    japanese_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 导演表
CREATE TABLE directors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    director_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    japanese_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系列表
CREATE TABLE series (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    series_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    japanese_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分类标签表
CREATE TABLE genres (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    genre_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    japanese_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 影片-演员关联表
CREATE TABLE movie_actors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    actor_id UUID REFERENCES actors(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'actress', -- 角色类型
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(movie_id, actor_id)
);

-- 影片-分类关联表
CREATE TABLE movie_genres (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    genre_id UUID REFERENCES genres(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(movie_id, genre_id)
);

-- 影片关联信息表（制作商、发行商等）
CREATE TABLE movie_relations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    studio_id UUID REFERENCES studios(id) ON DELETE SET NULL,
    publisher_id UUID REFERENCES publishers(id) ON DELETE SET NULL,
    director_id UUID REFERENCES directors(id) ON DELETE SET NULL,
    series_id UUID REFERENCES series(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 磁力链接表
CREATE TABLE magnets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    magnet_id VARCHAR(100) UNIQUE NOT NULL, -- 磁力链接ID
    link TEXT NOT NULL,
    title VARCHAR(500),
    size_text VARCHAR(50), -- 大小文本，如 "6.57GB"
    size_bytes BIGINT, -- 大小字节数
    share_date DATE,
    is_hd BOOLEAN DEFAULT FALSE,
    has_subtitle BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 预览图片表
CREATE TABLE sample_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    image_id VARCHAR(100) NOT NULL,
    url TEXT NOT NULL,
    thumbnail_url TEXT,
    local_path TEXT,
    alt_text TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据来源追踪表
CREATE TABLE metadata_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    source_name VARCHAR(100) NOT NULL, -- javbus, javlibrary, r18, etc.
    source_url TEXT,
    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_quality INTEGER DEFAULT 0, -- 数据质量评分 0-100
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    service_name VARCHAR(100), -- 服务名称，支持服务级配置
    config_type VARCHAR(50) DEFAULT 'general', -- 配置类型：general, service, feature
    is_active BOOLEAN DEFAULT TRUE, -- 配置是否激活
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_movies_movie_id ON movies(movie_id);
CREATE INDEX idx_movies_release_date ON movies(release_date);
CREATE INDEX idx_actors_actor_id ON actors(actor_id);
CREATE INDEX idx_actors_name ON actors(name);
CREATE INDEX idx_magnets_movie_id ON magnets(movie_id);
CREATE INDEX idx_sample_images_movie_id ON sample_images(movie_id);
CREATE INDEX idx_metadata_sources_movie_id ON metadata_sources(movie_id);
CREATE INDEX idx_system_config_service_name ON system_config(service_name);
CREATE INDEX idx_system_config_config_type ON system_config(config_type);
CREATE INDEX idx_system_config_is_active ON system_config(is_active);
CREATE INDEX idx_actor_avatar_sources_actor_id ON actor_avatar_sources(actor_id);
CREATE INDEX idx_actor_avatar_sources_source_name ON actor_avatar_sources(source_name);
CREATE INDEX idx_actor_avatar_sources_is_primary ON actor_avatar_sources(is_primary);
CREATE INDEX idx_data_integrity_checks_entity_type_id ON data_integrity_checks(entity_type, entity_id);
CREATE INDEX idx_data_integrity_checks_status ON data_integrity_checks(status);
CREATE INDEX idx_data_integrity_checks_severity ON data_integrity_checks(severity);
CREATE INDEX idx_incremental_update_tasks_status ON incremental_update_tasks(status);
CREATE INDEX idx_incremental_update_tasks_priority ON incremental_update_tasks(priority);
CREATE INDEX idx_incremental_update_tasks_entity_type_id ON incremental_update_tasks(entity_type, entity_id);
CREATE INDEX idx_incremental_update_tasks_scheduled_at ON incremental_update_tasks(scheduled_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_movies_updated_at BEFORE UPDATE ON movies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_actors_updated_at BEFORE UPDATE ON actors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 演员头像来源表
CREATE TABLE actor_avatar_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    actor_id UUID REFERENCES actors(id) ON DELETE CASCADE,
    source_name VARCHAR(100) NOT NULL, -- javdb, javlibrary, dmm, etc.
    avatar_url TEXT NOT NULL,
    local_path TEXT,
    quality_score INTEGER DEFAULT 0, -- 头像质量评分 0-100
    resolution VARCHAR(20), -- 分辨率，如 "400x400"
    file_size INTEGER, -- 文件大小（字节）
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主要头像
    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据完整性检测表
CREATE TABLE data_integrity_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_type VARCHAR(50) NOT NULL, -- movie, actor, etc.
    entity_id UUID NOT NULL, -- 对应实体的ID
    check_type VARCHAR(100) NOT NULL, -- missing_fields, quality_issues, etc.
    issue_description TEXT,
    severity VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    status VARCHAR(20) DEFAULT 'pending', -- pending, resolved, ignored
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 增量更新任务表
CREATE TABLE incremental_update_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_type VARCHAR(100) NOT NULL, -- actor_avatar_update, missing_data_completion, etc.
    entity_type VARCHAR(50) NOT NULL, -- movie, actor, etc.
    entity_id UUID, -- 对应实体的ID，可为空表示批量任务
    priority INTEGER DEFAULT 5, -- 优先级 1-10，数字越小优先级越高
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    progress INTEGER DEFAULT 0, -- 进度百分比 0-100
    config JSONB DEFAULT '{}', -- 任务配置参数
    result JSONB DEFAULT '{}', -- 任务执行结果
    error_message TEXT, -- 错误信息
    scheduled_at TIMESTAMP, -- 计划执行时间
    started_at TIMESTAMP, -- 开始执行时间
    completed_at TIMESTAMP, -- 完成时间
    retry_count INTEGER DEFAULT 0, -- 重试次数
    max_retries INTEGER DEFAULT 3, -- 最大重试次数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为新表创建更新时间触发器
CREATE TRIGGER update_actor_avatar_sources_updated_at BEFORE UPDATE ON actor_avatar_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_integrity_checks_updated_at BEFORE UPDATE ON data_integrity_checks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_incremental_update_tasks_updated_at BEFORE UPDATE ON incremental_update_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
