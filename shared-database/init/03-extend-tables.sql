-- JAV 数据库扩展脚本
-- 用于在现有数据库上应用新功能所需的表结构扩展

-- 设置编码
SET client_encoding = 'UTF8';

-- 扩展system_config表，添加服务级配置支持
ALTER TABLE system_config 
ADD COLUMN IF NOT EXISTS service_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS config_type VARCHAR(50) DEFAULT 'general',
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- 扩展actors表，添加多源数据支持
ALTER TABLE actors 
ADD COLUMN IF NOT EXISTS data_sources JSONB DEFAULT '{}';

-- 创建演员头像来源表
CREATE TABLE IF NOT EXISTS actor_avatar_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    actor_id UUID REFERENCES actors(id) ON DELETE CASCADE,
    source_name VARCHAR(100) NOT NULL, -- javdb, javlibrary, dmm, etc.
    avatar_url TEXT NOT NULL,
    local_path TEXT,
    quality_score INTEGER DEFAULT 0, -- 头像质量评分 0-100
    resolution VARCHAR(20), -- 分辨率，如 "400x400"
    file_size INTEGER, -- 文件大小（字节）
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主要头像
    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建数据完整性检测表
CREATE TABLE IF NOT EXISTS data_integrity_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_type VARCHAR(50) NOT NULL, -- movie, actor, etc.
    entity_id UUID NOT NULL, -- 对应实体的ID
    check_type VARCHAR(100) NOT NULL, -- missing_fields, quality_issues, etc.
    issue_description TEXT,
    severity VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    status VARCHAR(20) DEFAULT 'pending', -- pending, resolved, ignored
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建增量更新任务表
CREATE TABLE IF NOT EXISTS incremental_update_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_type VARCHAR(100) NOT NULL, -- actor_avatar_update, missing_data_completion, etc.
    entity_type VARCHAR(50) NOT NULL, -- movie, actor, etc.
    entity_id UUID, -- 对应实体的ID，可为空表示批量任务
    priority INTEGER DEFAULT 5, -- 优先级 1-10，数字越小优先级越高
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    progress INTEGER DEFAULT 0, -- 进度百分比 0-100
    config JSONB DEFAULT '{}', -- 任务配置参数
    result JSONB DEFAULT '{}', -- 任务执行结果
    error_message TEXT, -- 错误信息
    scheduled_at TIMESTAMP, -- 计划执行时间
    started_at TIMESTAMP, -- 开始执行时间
    completed_at TIMESTAMP, -- 完成时间
    retry_count INTEGER DEFAULT 0, -- 重试次数
    max_retries INTEGER DEFAULT 3, -- 最大重试次数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建新索引
CREATE INDEX IF NOT EXISTS idx_system_config_service_name ON system_config(service_name);
CREATE INDEX IF NOT EXISTS idx_system_config_config_type ON system_config(config_type);
CREATE INDEX IF NOT EXISTS idx_system_config_is_active ON system_config(is_active);
CREATE INDEX IF NOT EXISTS idx_actor_avatar_sources_actor_id ON actor_avatar_sources(actor_id);
CREATE INDEX IF NOT EXISTS idx_actor_avatar_sources_source_name ON actor_avatar_sources(source_name);
CREATE INDEX IF NOT EXISTS idx_actor_avatar_sources_is_primary ON actor_avatar_sources(is_primary);
CREATE INDEX IF NOT EXISTS idx_data_integrity_checks_entity_type_id ON data_integrity_checks(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_data_integrity_checks_status ON data_integrity_checks(status);
CREATE INDEX IF NOT EXISTS idx_data_integrity_checks_severity ON data_integrity_checks(severity);
CREATE INDEX IF NOT EXISTS idx_incremental_update_tasks_status ON incremental_update_tasks(status);
CREATE INDEX IF NOT EXISTS idx_incremental_update_tasks_priority ON incremental_update_tasks(priority);
CREATE INDEX IF NOT EXISTS idx_incremental_update_tasks_entity_type_id ON incremental_update_tasks(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_incremental_update_tasks_scheduled_at ON incremental_update_tasks(scheduled_at);

-- 为新表创建更新时间触发器（如果触发器函数存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
        -- 为新表创建触发器
        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_actor_avatar_sources_updated_at') THEN
            CREATE TRIGGER update_actor_avatar_sources_updated_at BEFORE UPDATE ON actor_avatar_sources
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_data_integrity_checks_updated_at') THEN
            CREATE TRIGGER update_data_integrity_checks_updated_at BEFORE UPDATE ON data_integrity_checks
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_incremental_update_tasks_updated_at') THEN
            CREATE TRIGGER update_incremental_update_tasks_updated_at BEFORE UPDATE ON incremental_update_tasks
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        END IF;
    END IF;
END $$;

-- 插入新的系统配置项
INSERT INTO system_config (config_key, config_value, description, service_name, config_type) VALUES
('multi_source_avatar_enabled', 'true', '启用多源演员头像采集', 'data-service', 'feature'),
('avatar_quality_threshold', '70', '头像质量评分阈值', 'data-service', 'feature'),
('incremental_update_interval', '24', '增量更新检查间隔（小时）', 'data-service', 'feature'),
('data_integrity_check_enabled', 'true', '启用数据完整性检测', 'data-service', 'feature'),
('max_concurrent_avatar_downloads', '3', '最大并发头像下载数', 'data-service', 'performance'),
('avatar_sources_priority', '["javdb", "javlibrary", "dmm"]', '头像数据源优先级', 'data-service', 'feature')
ON CONFLICT (config_key) DO NOTHING;

-- 创建用于数据完整性检测的函数
CREATE OR REPLACE FUNCTION check_actor_data_integrity()
RETURNS TABLE(actor_id UUID, missing_fields TEXT[], quality_score INTEGER) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        ARRAY(
            SELECT field_name FROM (
                VALUES
                    ('name', a.name IS NULL OR a.name = ''),
                    ('avatar_url', a.avatar_url IS NULL OR a.avatar_url = ''),
                    ('avatar_local_path', a.avatar_local_path IS NULL OR a.avatar_local_path = ''),
                    ('birthday', a.birthday IS NULL),
                    ('height', a.height IS NULL),
                    ('japanese_name', a.japanese_name IS NULL OR a.japanese_name = '')
            ) AS fields(field_name, is_missing)
            WHERE is_missing = TRUE
        ) AS missing_fields,
        CASE
            WHEN a.name IS NOT NULL AND a.name != '' THEN 20 ELSE 0
        END +
        CASE
            WHEN a.avatar_local_path IS NOT NULL AND a.avatar_local_path != '' THEN 25 ELSE 0
        END +
        CASE
            WHEN a.birthday IS NOT NULL THEN 15 ELSE 0
        END +
        CASE
            WHEN a.height IS NOT NULL THEN 10 ELSE 0
        END +
        CASE
            WHEN a.japanese_name IS NOT NULL AND a.japanese_name != '' THEN 15 ELSE 0
        END +
        CASE
            WHEN a.bust IS NOT NULL THEN 5 ELSE 0
        END +
        CASE
            WHEN a.waist IS NOT NULL THEN 5 ELSE 0
        END +
        CASE
            WHEN a.hip IS NOT NULL THEN 5 ELSE 0
        END AS quality_score
    FROM actors a;
END;
$$ LANGUAGE plpgsql;
